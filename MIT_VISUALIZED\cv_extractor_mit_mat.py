#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文件名: cv_extractor.py
描述: 恒压阶段提取器，用于从充放电数据中提取恒压阶段数据
      更新：使用更严格的电压范围条件(±0.002V)和电流变化率判断，
      精确识别恒压阶段，避免将恒流阶段过渡点错误识别为恒压阶段
作者: Claude
日期: 2023-07-10
版本: 1.2
"""

import numpy as np
from typing import Dict, List, Optional
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('cv_extractor')

class CVExtractor:
    """
    恒压阶段提取器类
    用于从充放电数据中识别和提取恒压阶段数据
    """
    
    def __init__(self, 
                 voltage_threshold: float = 4.2, 
                 tolerance: float = 0.01,
                 min_duration: float = 5.0,
                 min_points: int = 10,
                 current_min_threshold: float = 0.01,
                 current_rate_threshold: float = 0.05):
        """
        初始化恒压阶段提取器
        
        参数:
            voltage_threshold (float): 恒压阶段的电压阈值，默认为4.2V
            tolerance (float): 电压阈值的容差，默认为0.01V
            min_duration (float): 最小恒压持续时间(秒)，默认为5秒
            min_points (int): 最少恒压阶段数据点，默认为10个
            current_min_threshold (float): 最小电流阈值，小于此值视为弛豫阶段，默认为0.01A
            current_rate_threshold (float): 电流变化率阈值，用于区分恒流和恒压阶段，默认为0.05
        """
        self.voltage_threshold = voltage_threshold
        self.tolerance = tolerance
        self.min_duration = min_duration
        self.min_points = min_points
        self.current_min_threshold = current_min_threshold
        self.current_rate_threshold = current_rate_threshold
        
        logger.info(f"恒压提取器初始化: 阈值={voltage_threshold}V, 容差={tolerance}V, " +
                   f"最小持续时间={min_duration}秒, 最少点数={min_points}, 电流阈值={current_min_threshold}A, " +
                   f"电流变化率阈值={current_rate_threshold}")
    
    def extract_cv_phase(self, 
                         time: np.ndarray, 
                         voltage: np.ndarray, 
                         current: np.ndarray) -> Dict[str, np.ndarray]:
        """
        从充电数据中提取恒压阶段
        
        参数:
            time (np.ndarray): 时间数据, 单位秒
            voltage (np.ndarray): 电压数据, 单位伏特
            current (np.ndarray): 电流数据, 单位安培
            
        返回:
            Dict[str, np.ndarray]: 恒压阶段数据，包含时间、电压、电流
        """
        if not self._validate_input(time, voltage, current):
            return {}
            
        # 提取所有恒压阶段
        cv_phases = self._extract_all_cv_phases_internal(time, voltage, current)
        
        # 如果没有找到恒压阶段，返回空
        if not cv_phases:
            return {}
            
        # 找出持续时间最长的阶段
        longest_phase = max(cv_phases, key=lambda x: x['duration'])
        return longest_phase
    
    def extract_all_cv_phases(self, 
                              time: np.ndarray, 
                              voltage: np.ndarray, 
                              current: np.ndarray) -> List[Dict[str, np.ndarray]]:
        """
        从充电数据中提取所有恒压阶段
        
        参数:
            time (np.ndarray): 时间数据, 单位秒
            voltage (np.ndarray): 电压数据, 单位伏特
            current (np.ndarray): 电流数据, 单位安培
            
        返回:
            List[Dict[str, np.ndarray]]: 所有恒压阶段数据，每个元素包含时间、电压、电流
        """
        if not self._validate_input(time, voltage, current):
            return []
        
        return self._extract_all_cv_phases_internal(time, voltage, current)
    
    def _extract_all_cv_phases_internal(self, 
                                       time: np.ndarray, 
                                       voltage: np.ndarray, 
                                       current: np.ndarray) -> List[Dict[str, np.ndarray]]:
        """内部方法：提取所有恒压阶段"""
        # 寻找恒压阶段
        cv_mask = self._get_cv_mask(time, voltage, current)
        
        # 如果没有足够的点满足条件，直接返回空
        if np.sum(cv_mask) < self.min_points:
            logger.debug(f"恒压阶段数据点不足: {np.sum(cv_mask)} < {self.min_points}")
            return []
            
        # 找到连续的恒压阶段
        cv_indices = np.where(cv_mask)[0]
        segments = self._find_continuous_segments(cv_indices)
        
        # 处理所有区间
        cv_phases = []
        
        for start_idx, end_idx in segments:
            # 调整恒压段范围
            adjusted_start_idx = self._adjust_cv_phase_start(voltage, current, start_idx, end_idx)
            adjusted_end_idx = self._adjust_cv_phase_end(voltage, current, adjusted_start_idx, end_idx)
            
            # 检查持续时间和点数
            duration = time[adjusted_end_idx] - time[adjusted_start_idx]
            points = adjusted_end_idx - adjusted_start_idx + 1
            
            if duration >= self.min_duration and points >= self.min_points:
                # 提取恒压阶段数据
                cv_phase = self._extract_cv_phase_data(time, voltage, current, adjusted_start_idx, adjusted_end_idx)
                cv_phases.append(cv_phase)
                
                logger.debug(f"恒压段 {len(cv_phases)}: 点数={points}, 持续时间={duration:.2f}s")
        
        logger.debug(f"共提取到 {len(cv_phases)} 个恒压阶段")
        return cv_phases
    
    def _validate_input(self, time: np.ndarray, voltage: np.ndarray, current: np.ndarray) -> bool:
        """验证输入数据有效性"""
        if len(time) == 0 or len(voltage) == 0 or len(current) == 0:
            logger.warning("输入数据为空")
            return False
            
        if len(time) != len(voltage) or len(time) != len(current):
            logger.warning(f"数据长度不一致: 时间={len(time)}, 电压={len(voltage)}, 电流={len(current)}")
            return False
            
        return True
    
    def _get_cv_mask(self, time: np.ndarray, voltage: np.ndarray, current: np.ndarray) -> np.ndarray:
        """获取恒压阶段掩码（向量化操作）"""
        # 对于MIT_mat数据集，使用严格的电压范围
        v_min = self.voltage_threshold - 0.002  # 更精确的电压下限
        v_max = self.voltage_threshold + 0.002  # 更精确的电压上限
        
        # 计算电流的变化率
        current_rate = np.zeros_like(current)
        if len(current) > 1:
            # 计算相邻点的电流变化率
            current_diff = np.diff(current)
            time_diff = np.diff(time)
            # 避免除以零
            time_diff = np.where(time_diff == 0, 1e-6, time_diff)
            # 电流变化率（绝对值）
            rate = np.abs(current_diff / time_diff)
            
            # 由于使用diff，长度减少1，在末尾补充最后一个点的变化率
            current_rate[:-1] = rate
            current_rate[-1] = rate[-1]
            
            # 为了避免前几个点的误判，对电流变化率进行平滑处理
            if len(current_rate) > 5:
                window_size = 5
                current_rate_padded = np.pad(current_rate, (window_size//2, window_size//2), mode='edge')
                current_rate = np.convolve(current_rate_padded, np.ones(window_size)/window_size, mode='valid')
        
        # 恒压阶段条件：
        # 1. 电压在阈值的严格范围内(3.6±0.002V)
        # 2. 电流大于最小阈值（排除弛豫阶段）
        # 3. 电流变化率小于阈值（排除恒流阶段）
        voltage_cond = (voltage >= v_min) & (voltage <= v_max)
        current_cond = (np.abs(current) > self.current_min_threshold)
        rate_cond = (current_rate < self.current_rate_threshold)
        
        # 电流恒定条件（排除恒流点，1.0±0.002A）
        constant_current_min = 0.998  # 恒流阶段电流下限
        constant_current_max = 1.002  # 恒流阶段电流上限
        not_cc_cond = ~((current >= constant_current_min) & (current <= constant_current_max))
        
        # 使用更严格的条件过滤
        cv_mask = voltage_cond & current_cond & rate_cond & not_cc_cond
        
        return cv_mask
        
    def _find_continuous_segments(self, indices: np.ndarray) -> List[tuple]:
        """找出连续的索引区间（向量化操作）"""
        if len(indices) == 0:
            return []
        
        # 计算相邻索引的差值
        index_diffs = np.diff(indices)
        
        # 找出不连续的点(差值大于1)
        break_points = np.where(index_diffs > 1)[0]
        
        # 将所有连续区间的起始结束索引放入列表
        segments = []
        
        if len(break_points) == 0:
            # 只有一个连续区间
            segments.append((indices[0], indices[-1]))
        else:
            # 多个连续区间
            # 计算所有区间的起始点
            starts = np.concatenate(([indices[0]], indices[break_points + 1]))
            # 计算所有区间的结束点
            ends = np.concatenate((indices[break_points], [indices[-1]]))
            
            # 合并为区间列表
            segments = list(zip(starts, ends))
        
        return segments
    
    def _adjust_cv_phase_start(self, voltage: np.ndarray, current: np.ndarray, 
                              start_idx: int, end_idx: int) -> int:
        """调整恒压阶段起始点，确保不包含恒流阶段数据"""
        # 原始起始点
        original_start_idx = start_idx
        
        # 1. 严格的电压条件：确保电压在恒压阈值的±0.002V范围内
        v_strict_min = self.voltage_threshold - 0.002
        v_strict_max = self.voltage_threshold + 0.002
        
        # 2. 恒流点条件：确保电流在1.0±0.002A范围内的点最多只有1个
        constant_current_min = 0.998  # 恒流阶段电流下限
        constant_current_max = 1.002  # 恒流阶段电流上限
        
        # 计算每个点是否是恒流点
        cc_points = []
        for i in range(start_idx, end_idx + 1):
            if constant_current_min <= np.abs(current[i]) <= constant_current_max:
                cc_points.append(i)
        
        # 如果恒流点超过1个，则从第二个恒流点开始
        adjusted_start_idx = start_idx
        if len(cc_points) > 1:
            adjusted_start_idx = cc_points[1]  # 从第二个恒流点开始
        
        # 确保起始点的电压在严格范围内
        voltage_in_range = False
        for i in range(adjusted_start_idx, end_idx + 1):
            if v_strict_min <= voltage[i] <= v_strict_max:
                adjusted_start_idx = i
                voltage_in_range = True
                break
        
        # 如果没有找到电压在范围内的点，保持原起始点
        if not voltage_in_range:
            adjusted_start_idx = original_start_idx
            
        # 如果调整了起始点，记录日志
        if adjusted_start_idx != original_start_idx:
            logger.debug(f"调整恒压阶段起始点: {original_start_idx} -> {adjusted_start_idx}")
            
        return adjusted_start_idx
    
    def _adjust_cv_phase_end(self, voltage: np.ndarray, current: np.ndarray, 
                             start_idx: int, end_idx: int) -> int:
        """调整恒压阶段结束点"""
        # 尝试向后延伸恒压段，直到遇到电压超出范围或电流小于阈值的点
        original_end_idx = end_idx
        max_idx = len(current) - 1
        
        # 向后搜索符合条件的点
        while end_idx < max_idx:
            next_idx = end_idx + 1
            if (np.abs(current[next_idx]) > self.current_min_threshold and 
                abs(voltage[next_idx] - self.voltage_threshold) <= self.tolerance):
                end_idx = next_idx
            else:
                break
        
        # 向后搜索，剔除结束部分电流接近0的弛豫区域
        searching_end_idx = end_idx
        while searching_end_idx > start_idx and np.abs(current[searching_end_idx]) <= self.current_min_threshold:
            searching_end_idx -= 1
        
        # 如果找到了弛豫阶段，则调整end_idx
        if searching_end_idx < end_idx:
            logger.debug(f"截断恒压阶段末端弛豫部分: {end_idx} -> {searching_end_idx}")
            end_idx = searching_end_idx
            
        return end_idx
    
    def _extract_cv_phase_data(self, 
                              time: np.ndarray, 
                              voltage: np.ndarray, 
                              current: np.ndarray, 
                              start_idx: int, 
                              end_idx: int) -> Dict[str, np.ndarray]:
        """提取恒压阶段数据"""
        # 使用向量化切片操作
        cv_time = time[start_idx:end_idx+1]
        cv_voltage = voltage[start_idx:end_idx+1]
        cv_current = current[start_idx:end_idx+1]
                    
        # 计算统计信息
        v_mean = np.mean(cv_voltage)
        v_std = np.std(cv_voltage)
        i_start = cv_current[0]
        i_end = cv_current[-1]
        duration = cv_time[-1] - cv_time[0]
        
        # 提取前三个点和后三个点的信息
        initial_points = []
        for i in range(min(3, len(cv_time))):
            initial_points.append({
                'time': float(cv_time[i]),
                'voltage': float(cv_voltage[i]),
                'current': float(cv_current[i])
            })
            
        final_points = []
        for i in range(max(0, len(cv_time)-3), len(cv_time)):
            final_points.append({
                'time': float(cv_time[i]),
                'voltage': float(cv_voltage[i]),
                'current': float(cv_current[i])
            })
        
        # 提取CV阶段前三个点和后三个点的信息（如果有）
        before_points = []
        for i in range(max(0, start_idx-3), start_idx):
            if i >= 0 and i < len(time):
                before_points.append({
                    'time': float(time[i]),
                    'voltage': float(voltage[i]),
                    'current': float(current[i])
                })
                
        after_points = []
        for i in range(end_idx+1, min(end_idx+4, len(time))):
            if i < len(time):
                after_points.append({
                    'time': float(time[i]),
                    'voltage': float(voltage[i]),
                    'current': float(current[i])
                })
        
        logger.debug(f"提取恒压阶段: {len(cv_time)}点, 时间={cv_time[0]:.1f}-{cv_time[-1]:.1f}s, " +
                    f"电压={v_mean:.3f}±{v_std:.3f}V, 电流={i_start:.3f}-{i_end:.3f}A")
        
        return {
            'time': cv_time,
            'voltage': cv_voltage,
            'current': cv_current,
            'start_index': start_idx,
            'end_index': end_idx,
            'duration': duration,
            'v_mean': v_mean,
            'v_std': v_std,
            'i_start': i_start,
            'i_end': i_end,
            'initial_points': initial_points,
            'final_points': final_points,
            'before_points': before_points,
            'after_points': after_points
        }
    
    def is_valid_cv_phase(self, time: np.ndarray, voltage: np.ndarray, 
                         current: np.ndarray, strict: bool = False) -> bool:
        """
        判断给定的数据是否为有效的恒压阶段
        
        参数:
            time (np.ndarray): 时间数据, 单位秒
            voltage (np.ndarray): 电压数据, 单位伏特
            current (np.ndarray): 电流数据, 单位安培
            strict (bool): 是否使用严格模式，默认为False
            
        返回:
            bool: 是否为有效恒压阶段
        """
        if len(time) < self.min_points:
            return False
            
        # 检查持续时间
        duration = time[-1] - time[0]
        if duration < self.min_duration:
            return False
            
        # 检查电流是否全部大于阈值（过滤弛豫阶段）
        if not np.all(np.abs(current) > self.current_min_threshold):
            return False
        
        # 计算电压统计信息
        v_mean = np.mean(voltage)
        v_std = np.std(voltage)
        
        # 非严格模式下，只要电压在阈值范围内即可
        if not strict:
            return (v_mean >= self.voltage_threshold - self.tolerance and 
                    v_mean <= self.voltage_threshold + self.tolerance)
        
        # 严格模式下，要求电压标准差很小，且电流单调递减
        if v_std > self.tolerance / 3:
            return False
            
        # 检查电流是否为单调递减
        return self._is_decreasing(current)
    
    def _is_decreasing(self, data: np.ndarray, max_violations: int = 3) -> bool:
        """检查数据是否基本呈单调递减趋势（向量化操作）"""
        # 计算相邻元素的差值
        diffs = np.diff(data)
        
        # 计算违反递减趋势的次数（差值大于0）
        violations = np.sum(diffs > 0)
        
        return violations <= max_violations
    
    def adjust_threshold(self, voltage: np.ndarray) -> float:
        """
        根据电压数据自动调整恒压阈值
        
        参数:
            voltage (np.ndarray): 电压数据
            
        返回:
            float: 调整后的恒压阈值
        """
        if len(voltage) == 0:
            return self.voltage_threshold
            
        # 找到最大值作为恒压阈值
        max_voltage = np.max(voltage)
        
        # 只有当最大电压与当前阈值差异较大时才调整
        if abs(max_voltage - self.voltage_threshold) > 0.1:
            logger.info(f"调整恒压阈值: {self.voltage_threshold}V -> {max_voltage}V")
            self.voltage_threshold = max_voltage
            
        return self.voltage_threshold
    
    def set_params(self, voltage_threshold: Optional[float] = None, 
                   tolerance: Optional[float] = None,
                   min_duration: Optional[float] = None,
                   min_points: Optional[int] = None,
                   current_min_threshold: Optional[float] = None,
                   current_rate_threshold: Optional[float] = None) -> None:
        """
        设置恒压提取器参数
        
        参数:
            voltage_threshold (float, 可选): 恒压阶段的电压阈值
            tolerance (float, 可选): 电压阈值的容差
            min_duration (float, 可选): 最小恒压持续时间(秒)
            min_points (int, 可选): 最少恒压阶段数据点
            current_min_threshold (float, 可选): 最小电流阈值，小于此值视为弛豫阶段
            current_rate_threshold (float, 可选): 电流变化率阈值，用于区分恒流和恒压阶段
        """
        params_updated = False
        
        if voltage_threshold is not None:
            self.voltage_threshold = voltage_threshold
            params_updated = True
            
        if tolerance is not None:
            self.tolerance = tolerance
            params_updated = True
            
        if min_duration is not None:
            self.min_duration = min_duration
            params_updated = True
            
        if min_points is not None:
            self.min_points = min_points
            params_updated = True
            
        if current_min_threshold is not None:
            self.current_min_threshold = current_min_threshold
            params_updated = True
            
        if current_rate_threshold is not None:
            self.current_rate_threshold = current_rate_threshold
            params_updated = True
            
        if params_updated:
            logger.info(f"恒压提取器参数更新: 阈值={self.voltage_threshold}V, 容差={self.tolerance}V, " +
                       f"最小持续时间={self.min_duration}秒, 最少点数={self.min_points}, " +
                       f"电流阈值={self.current_min_threshold}A, " +
                       f"电流变化率阈值={self.current_rate_threshold}") 
