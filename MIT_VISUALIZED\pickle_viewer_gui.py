import sys
import os
import pickle
import pandas as pd
import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QFileDialog, QTreeView, QTreeWidget, QTreeWidgetItem,
                             QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QTableView, QTabWidget,
                             QSplitter, QLabel, QMessageBox, QHeaderView, QProgressDialog, QMenu,
                             QAction, QFrame, QToolTip)
from PyQt5.QtCore import Qt, QAbstractTableModel, QModelIndex, QMimeData, QThread, pyqtSignal, QSize, QPoint, QByteArray
from PyQt5.QtGui import (QFont, QColor, QDragEnterEvent, QDropEvent, QDrag, QCursor, QPainter, QPen, QBrush,
                        QPixmap, QFontMetrics)

# 添加常量定义，用于控制大文件处理
MAX_DISPLAY_ROWS = 1000  # 表格最大显示行数
MAX_DISPLAY_COLS = 100   # 表格最大显示列数
MAX_TREE_ITEMS = 100     # 树状视图中每层最大显示项数
MAX_PREVIEW_SIZE = 50    # 预览字符串长度
LARGE_DF_THRESHOLD = 10000  # 大型DataFrame行数阈值

class PandasModel(QAbstractTableModel):
    """用于在QTableView中显示pandas DataFrame的模型"""
    
    def __init__(self, data):
        super().__init__()
        # 限制大型DataFrame的显示行数和列数
        if isinstance(data, pd.DataFrame):
            if data.shape[0] > MAX_DISPLAY_ROWS or data.shape[1] > MAX_DISPLAY_COLS:
                # 如果数据过大，只显示部分数据
                rows_to_show = min(data.shape[0], MAX_DISPLAY_ROWS)
                cols_to_show = min(data.shape[1], MAX_DISPLAY_COLS)
                self._data = data.iloc[:rows_to_show, :cols_to_show].copy()
                self._truncated = True
                self._original_shape = data.shape
            else:
                self._data = data
                self._truncated = False
        else:
            self._data = data
            self._truncated = False

    def rowCount(self, parent=QModelIndex()):
        return self._data.shape[0]

    def columnCount(self, parent=QModelIndex()):
        return self._data.shape[1]

    def data(self, index, role=Qt.DisplayRole):
        if index.isValid():
            if role == Qt.DisplayRole:
                # 优化数据显示，避免处理大型字符串
                value = self._data.iloc[index.row(), index.column()]
                if isinstance(value, (str, bytes)) and len(str(value)) > MAX_PREVIEW_SIZE:
                    return str(value)[:MAX_PREVIEW_SIZE] + "..."
                return str(value)
            if role == Qt.BackgroundRole and index.row() % 2 == 0:
                return QColor(Qt.lightGray)
            if role == Qt.ToolTipRole and self._truncated:
                return "数据已截断显示，完整数据大小: {}行 x {}列".format(
                    self._original_shape[0], self._original_shape[1])
        return None

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole:
            if orientation == Qt.Horizontal:
                return str(self._data.columns[section])
            if orientation == Qt.Vertical:
                return str(self._data.index[section])
        return None


class PickleLoaderThread(QThread):
    """用于异步加载pickle文件的线程"""
    finished = pyqtSignal(object, str, bool)  # 数据, 文件路径, 成功/失败
    progress = pyqtSignal(int)  # 进度信号
    
    def __init__(self, file_path):
        super().__init__()
        self.file_path = file_path
        
    def run(self):
        try:
            # 获取文件大小以估计加载时间
            file_size = os.path.getsize(self.file_path)
            self.progress.emit(10)  # 初始进度
            
            with open(self.file_path, 'rb') as f:
                data = pickle.load(f)
            
            self.progress.emit(90)  # 加载完成
            self.finished.emit(data, self.file_path, True)
        except Exception as e:
            self.finished.emit(None, str(e), False)


class LazyTreeItem(QTreeWidgetItem):
    """延迟加载的树状项"""
    
    def __init__(self, parent, text, data_getter, is_lazy=True):
        super().__init__(parent, [text])
        self.data_getter = data_getter
        self.is_lazy = is_lazy
        self.is_loaded = False
        
        # 如果是延迟加载项，添加一个临时子项
        if is_lazy:
            self.addChild(QTreeWidgetItem(["加载中..."]))
    
    def load_children(self):
        """加载子项"""
        if self.is_lazy and not self.is_loaded:
            # 清除临时子项
            self.takeChildren()
            
            # 获取数据并加载子项
            data = self.data_getter()
            if data is not None:
                if isinstance(data, dict):
                    self.load_dict(data)
                elif isinstance(data, (list, tuple, np.ndarray)):
                    self.load_sequence(data)
                elif isinstance(data, pd.DataFrame):
                    self.load_dataframe(data)
                elif isinstance(data, pd.Series):
                    self.load_series(data)
            
            self.is_loaded = True
    
    def load_dict(self, data):
        """加载字典类型的数据"""
        keys = list(data.keys())
        if len(keys) > MAX_TREE_ITEMS:
            # 如果键太多，只显示部分
            for key in keys[:MAX_TREE_ITEMS]:
                LazyTreeItem(self, str(key), lambda k=key: data[k])
            self.addChild(QTreeWidgetItem([f"... 还有 {len(keys) - MAX_TREE_ITEMS} 个项目"]))
        else:
            for key in keys:
                LazyTreeItem(self, str(key), lambda k=key: data[k])
    
    def load_sequence(self, data):
        """加载序列类型的数据"""
        if len(data) > MAX_TREE_ITEMS:
            for i in range(MAX_TREE_ITEMS):
                LazyTreeItem(self, f"[{i}]", lambda idx=i: data[idx])
            self.addChild(QTreeWidgetItem([f"... 还有 {len(data) - MAX_TREE_ITEMS} 个项目"]))
        else:
            for i, item in enumerate(data):
                LazyTreeItem(self, f"[{i}]", lambda idx=i: data[idx])
    
    def load_dataframe(self, df):
        """加载DataFrame类型的数据"""
        # 列信息
        cols_item = LazyTreeItem(self, "columns", lambda: pd.Series(df.columns), False)
        for col in df.columns[:MAX_TREE_ITEMS]:
            cols_item.addChild(QTreeWidgetItem([str(col)]))
        if len(df.columns) > MAX_TREE_ITEMS:
            cols_item.addChild(QTreeWidgetItem([f"... 还有 {len(df.columns) - MAX_TREE_ITEMS} 列"]))
        
        # 数据预览
        preview_item = LazyTreeItem(self, "data (preview)", lambda: df.head(5), False)
        for i in range(min(5, df.shape[0])):
            preview_item.addChild(QTreeWidgetItem([f"行 {i}"]))
        
        # 添加数据大小信息
        self.addChild(QTreeWidgetItem([f"大小: {df.shape[0]}行 x {df.shape[1]}列"]))
    
    def load_series(self, series):
        """加载Series类型的数据"""
        # 索引信息
        index_item = LazyTreeItem(self, "index", lambda: pd.Series(series.index), False)
        for i in range(min(MAX_TREE_ITEMS, len(series.index))):
            index_item.addChild(QTreeWidgetItem([str(series.index[i])]))
        if len(series.index) > MAX_TREE_ITEMS:
            index_item.addChild(QTreeWidgetItem([f"... 还有 {len(series.index) - MAX_TREE_ITEMS} 个索引"]))
        
        # 值预览
        values_item = LazyTreeItem(self, "values (preview)", lambda: series.head(5), False)
        for i in range(min(5, len(series))):
            val = str(series.iloc[i])
            if len(val) > MAX_PREVIEW_SIZE:
                val = val[:MAX_PREVIEW_SIZE] + "..."
            values_item.addChild(QTreeWidgetItem([f"{i}: {val}"]))
        
        # 添加数据大小信息
        self.addChild(QTreeWidgetItem([f"大小: {len(series)}个元素"]))


class DragDropTableView(QTableView):
    """支持拖拽导出的表格视图"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragEnabled(True)
        self.setAcceptDrops(True)
        self.setSelectionMode(QTableView.ContiguousSelection)
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
        self.data_exporter = None  # 由父窗口设置
        self.drag_start_position = None
        self.setDragDropMode(QTableView.DragOnly)  # 只允许拖出，不允许拖入
    
    def mousePressEvent(self, event):
        """处理鼠标按下事件，准备拖拽"""
        super().mousePressEvent(event)
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.pos()
    
    def mouseMoveEvent(self, event):
        """处理鼠标移动事件，开始拖拽"""
        if not (event.buttons() & Qt.LeftButton) or self.drag_start_position is None:
            return
        
        # 确保移动距离足够大才开始拖拽
        if ((event.pos() - self.drag_start_position).manhattanLength() 
                < QApplication.startDragDistance()):
            return
        
        # 获取选中的索引
        selected_indexes = self.selectedIndexes()
        if not selected_indexes:
            return
        
        # 创建拖拽对象
        drag = QDrag(self)
        mime_data = QMimeData()
        
        # 获取选中数据
        model = self.model()
        if model is None:
            return
        
        # 将选中数据转换为文本
        text_data = self.selection_to_text(selected_indexes)
        mime_data.setText(text_data)
        
        # 如果有导出器，添加pickle数据
        if self.data_exporter:
            pickle_data = self.data_exporter.get_selected_data(selected_indexes)
            if pickle_data is not None:
                # 序列化数据
                byte_data = QByteArray(pickle.dumps(pickle_data))
                mime_data.setData("application/x-pickle", byte_data)
        
        # 创建拖拽预览图像
        pixmap = self.create_drag_pixmap(selected_indexes)
        if pixmap:
            drag.setPixmap(pixmap)
            drag.setHotSpot(QPoint(pixmap.width() // 2, pixmap.height() // 2))
        
        # 设置拖拽数据
        drag.setMimeData(mime_data)
        
        # 显示状态栏消息
        if hasattr(self.parent(), 'statusBar'):
            try:
                self.parent().statusBar().showMessage('正在拖拽数据...')
            except:
                pass
        
        # 执行拖拽
        result = drag.exec_(Qt.CopyAction)
        
        # 恢复状态栏消息
        if hasattr(self.parent(), 'statusBar'):
            try:
                if result == Qt.CopyAction:
                    self.parent().statusBar().showMessage('数据已成功导出', 3000)
                else:
                    self.parent().statusBar().showMessage('准备就绪。您可以拖放Pickle文件到窗口中加载，或者从表格中拖出数据。')
            except:
                pass
    
    def create_drag_pixmap(self, indexes):
        """创建拖拽预览图像"""
        if not indexes:
            return None
        
        # 找出选择区域的行列范围
        min_row = min(idx.row() for idx in indexes)
        max_row = max(idx.row() for idx in indexes)
        min_col = min(idx.column() for idx in indexes)
        max_col = max(idx.column() for idx in indexes)
        
        # 计算选中的行数和列数
        rows = max_row - min_row + 1
        cols = max_col - min_col + 1
        
        # 创建预览文本
        preview_text = f"{rows}行 x {cols}列"
        
        # 创建预览图像
        font = QFont("Arial", 10)
        font_metrics = QFontMetrics(font)
        text_width = font_metrics.width(preview_text)
        text_height = font_metrics.height()
        
        pixmap = QPixmap(text_width + 20, text_height + 10)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setRenderHint(QPainter.TextAntialiasing)
        
        # 绘制背景
        painter.setBrush(QBrush(QColor(230, 230, 250, 200)))
        painter.setPen(QPen(QColor(70, 130, 180), 1))
        painter.drawRoundedRect(0, 0, pixmap.width(), pixmap.height(), 5, 5)
        
        # 绘制文本
        painter.setPen(QColor(0, 0, 0))
        painter.setFont(font)
        painter.drawText(10, text_height + 2, preview_text)
        
        painter.end()
        return pixmap
    
    def selection_to_text(self, indexes):
        """将选中的单元格转换为制表符分隔的文本"""
        if not indexes:
            return ""
        
        model = self.model()
        
        # 找出选择区域的行列范围
        min_row = min(idx.row() for idx in indexes)
        max_row = max(idx.row() for idx in indexes)
        min_col = min(idx.column() for idx in indexes)
        max_col = max(idx.column() for idx in indexes)
        
        # 构建文本数据
        result = []
        
        # 添加列标题
        if isinstance(model, PandasModel) and hasattr(model, '_data') and hasattr(model._data, 'columns'):
            header_row = []
            for col in range(min_col, max_col + 1):
                if col < len(model._data.columns):
                    header_row.append(str(model._data.columns[col]))
                else:
                    header_row.append(f"Column {col}")
            result.append("\t".join(header_row))
        
        # 添加数据行
        for row in range(min_row, max_row + 1):
            row_data = []
            for col in range(min_col, max_col + 1):
                index = model.index(row, col)
                data = model.data(index, Qt.DisplayRole)
                row_data.append(str(data) if data is not None else "")
            result.append("\t".join(row_data))
        
        return "\n".join(result)
    
    def show_context_menu(self, pos):
        """显示右键菜单"""
        if not self.selectedIndexes():
            return
        
        menu = QMenu(self)
        export_action = QAction("导出选中数据到Pickle文件", self)
        export_action.triggered.connect(self.export_selection)
        menu.addAction(export_action)
        
        copy_action = QAction("复制到剪贴板", self)
        copy_action.triggered.connect(self.copy_selection)
        menu.addAction(copy_action)
        
        menu.exec_(self.viewport().mapToGlobal(pos))
    
    def export_selection(self):
        """导出选中数据到pickle文件"""
        if not self.data_exporter:
            return
        
        selected_indexes = self.selectedIndexes()
        if not selected_indexes:
            return
        
        data = self.data_exporter.get_selected_data(selected_indexes)
        if data is None:
            QMessageBox.warning(self, "导出失败", "无法导出选中的数据")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出到Pickle文件", "", "Pickle文件 (*.pkl *.pickle)")
        
        if file_path:
            try:
                with open(file_path, 'wb') as f:
                    pickle.dump(data, f)
                QMessageBox.information(self, "导出成功", f"数据已成功导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"导出数据时出错: {str(e)}")
    
    def copy_selection(self):
        """复制选中数据到剪贴板"""
        selected_indexes = self.selectedIndexes()
        if not selected_indexes:
            return
        
        text_data = self.selection_to_text(selected_indexes)
        QApplication.clipboard().setText(text_data)
        
        # 显示状态栏消息
        if hasattr(self.parent(), 'statusBar'):
            try:
                self.parent().statusBar().showMessage('数据已复制到剪贴板', 3000)
            except:
                pass


class DataExporter:
    """处理数据导出的辅助类"""
    
    def __init__(self, parent):
        self.parent = parent
    
    def get_selected_data(self, selected_indexes):
        """从选中的索引获取数据"""
        if not selected_indexes:
            return None
        
        model = selected_indexes[0].model()
        if not isinstance(model, PandasModel):
            return None
        
        # 找出选择区域的行列范围
        min_row = min(idx.row() for idx in selected_indexes)
        max_row = max(idx.row() for idx in selected_indexes)
        min_col = min(idx.column() for idx in selected_indexes)
        max_col = max(idx.column() for idx in selected_indexes)
        
        # 获取原始DataFrame
        df = model._data
        
        # 提取选中区域
        selected_df = df.iloc[min_row:max_row+1, min_col:max_col+1].copy()
        return selected_df


class DropIndicatorFrame(QFrame):
    """显示拖放指示的框架"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.StyledPanel)
        self.setFrameShadow(QFrame.Sunken)
        self.setAcceptDrops(True)
        self.setMinimumHeight(80)
        self.setToolTip("将Pickle文件拖放到此处加载")
        
        # 创建布局
        layout = QVBoxLayout(self)
        self.label = QLabel("拖放Pickle文件到这里")
        self.label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.label)
        
        # 样式设置
        self.setStyleSheet("""
            DropIndicatorFrame {
                border: 2px dashed #aaaaaa;
                border-radius: 5px;
                background-color: #f0f0f0;
            }
            DropIndicatorFrame:hover {
                border-color: #3daee9;
            }
        """)
        
        # 拖拽状态标志
        self.is_drag_over = False
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """处理拖拽进入事件"""
        self.is_drag_over = False
        # 检查是否是可接受的数据类型
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if len(urls) == 1:  # 只接受单个文件
                file_path = urls[0].toLocalFile()
                if file_path.endswith(('.pkl', '.pickle')):
                    self.is_drag_over = True
                    self.setStyleSheet("""
                        DropIndicatorFrame {
                            border: 2px dashed #3daee9;
                            border-radius: 5px;
                            background-color: #e0f0ff;
                        }
                    """)
                    self.label.setText("松开鼠标导入文件")
                    self.label.setStyleSheet("color: #3daee9; font-weight: bold;")
                    event.accept()  # 明确接受拖拽
                    event.acceptProposedAction()
                    return
        
        # 如果不是可接受的文件类型
        self.setStyleSheet("""
            DropIndicatorFrame {
                border: 2px dashed #ff6b6b;
                border-radius: 5px;
                background-color: #ffeded;
            }
        """)
        self.label.setText("不支持此类型文件")
        self.label.setStyleSheet("color: #ff6b6b;")
        event.ignore()  # 明确拒绝
    
    def dragLeaveEvent(self, event):
        """处理拖拽离开事件"""
        self.is_drag_over = False
        self.setStyleSheet("""
            DropIndicatorFrame {
                border: 2px dashed #aaaaaa;
                border-radius: 5px;
                background-color: #f0f0f0;
            }
            DropIndicatorFrame:hover {
                border-color: #3daee9;
            }
        """)
        self.label.setText("拖放Pickle文件到这里")
        self.label.setStyleSheet("")
        event.accept()
    
    def dragMoveEvent(self, event):
        """处理拖拽移动事件"""
        # 必须重写这个方法并接受事件，否则可能出现禁止标识
        if self.is_drag_over:
            event.accept()
            event.acceptProposedAction()
        else:
            event.ignore()
    
    def dropEvent(self, event: QDropEvent):
        """处理拖放事件"""
        if not self.is_drag_over:
            event.ignore()
            return
            
        self.is_drag_over = False
        self.setStyleSheet("""
            DropIndicatorFrame {
                border: 2px dashed #aaaaaa;
                border-radius: 5px;
                background-color: #f0f0f0;
            }
            DropIndicatorFrame:hover {
                border-color: #3daee9;
            }
        """)
        self.label.setText("拖放Pickle文件到这里")
        self.label.setStyleSheet("")
        
        # 将事件传递给父窗口处理
        if hasattr(self.parent(), "handle_drop_event"):
            self.parent().handle_drop_event(event)


class PickleViewerApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.pickle_data = None
        self.current_key = None
        self.loader_thread = None
        self.progress_dialog = None
        self.data_exporter = DataExporter(self)
        self.init_ui()
        
        # 启用拖放功能
        self.setAcceptDrops(True)
        
    def init_ui(self):
        self.setWindowTitle('Pickle文件可视化查看器')
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建顶部按钮布局
        button_layout = QHBoxLayout()
        
        # 打开文件按钮
        self.open_button = QPushButton('打开Pickle文件')
        self.open_button.clicked.connect(self.open_file)
        button_layout.addWidget(self.open_button)
        
        # 文件路径标签
        self.file_label = QLabel('未选择文件')
        button_layout.addWidget(self.file_label, stretch=1)
        
        main_layout.addLayout(button_layout)
        
        # 添加拖放指示区域
        self.drop_indicator = DropIndicatorFrame()
        main_layout.addWidget(self.drop_indicator)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 创建左侧树状视图
        self.tree_widget = QTreeWidget()
        self.tree_widget.setHeaderLabel('Pickle结构')
        self.tree_widget.itemClicked.connect(self.on_tree_item_clicked)
        self.tree_widget.itemExpanded.connect(self.on_tree_item_expanded)
        self.tree_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tree_widget.customContextMenuRequested.connect(self.show_tree_context_menu)
        splitter.addWidget(self.tree_widget)
        
        # 创建右侧表格视图容器
        self.tab_widget = QTabWidget()
        
        # 使用支持拖拽的表格视图
        self.normal_table = DragDropTableView()
        self.normal_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.normal_table.setVerticalScrollMode(QTableView.ScrollPerPixel)
        self.normal_table.setHorizontalScrollMode(QTableView.ScrollPerPixel)
        self.normal_table.data_exporter = self.data_exporter
        
        self.transposed_table = DragDropTableView()
        self.transposed_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.transposed_table.setVerticalScrollMode(QTableView.ScrollPerPixel)
        self.transposed_table.setHorizontalScrollMode(QTableView.ScrollPerPixel)
        self.transposed_table.data_exporter = self.data_exporter
        
        self.tab_widget.addTab(self.normal_table, '普通视图')
        self.tab_widget.addTab(self.transposed_table, '转置视图')
        splitter.addWidget(self.tab_widget)
        
        # 设置分割器比例
        splitter.setSizes([300, 900])
        
        main_layout.addWidget(splitter)
        
        # 添加状态栏
        self.statusBar().showMessage('准备就绪。您可以拖放Pickle文件到窗口中加载，或者从表格中拖出数据。')
    
    def show_tree_context_menu(self, pos):
        """显示树状视图的右键菜单"""
        item = self.tree_widget.itemAt(pos)
        if not item:
            return
        
        path = self.get_item_path(item)
        if not path:
            return
        
        # 获取选中项的数据
        data = self.get_data_from_path(path)
        if data is None:
            return
        
        menu = QMenu(self)
        export_action = QAction("导出到Pickle文件", self)
        export_action.triggered.connect(lambda: self.export_tree_item(data, path[-1]))
        menu.addAction(export_action)
        
        menu.exec_(self.tree_widget.viewport().mapToGlobal(pos))
    
    def export_tree_item(self, data, name):
        """导出树状项数据到pickle文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出到Pickle文件", f"{name}.pkl", "Pickle文件 (*.pkl *.pickle)")
        
        if file_path:
            try:
                with open(file_path, 'wb') as f:
                    pickle.dump(data, f)
                QMessageBox.information(self, "导出成功", f"数据已成功导出到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"导出数据时出错: {str(e)}")
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """处理拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查是否有文件被拖入，且是否为pickle文件
            urls = event.mimeData().urls()
            if len(urls) == 1:  # 只接受单个文件
                file_path = urls[0].toLocalFile()
                if file_path.endswith(('.pkl', '.pickle')):
                    # 更新状态栏
                    self.statusBar().showMessage(f'可以释放鼠标按键导入: {os.path.basename(file_path)}')
                    event.accept()
                    event.acceptProposedAction()
                    return
        
        # 拒绝不支持的类型
        event.ignore()
    
    def dragMoveEvent(self, event):
        """处理拖拽移动事件"""
        # 必须重写这个方法并接受事件，否则可能出现禁止标识
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if len(urls) == 1:  # 只接受单个文件
                file_path = urls[0].toLocalFile()
                if file_path.endswith(('.pkl', '.pickle')):
                    event.accept()
                    event.acceptProposedAction()
                    return
        event.ignore()
    
    def dragLeaveEvent(self, event):
        """处理拖拽离开事件"""
        self.statusBar().showMessage('准备就绪。您可以拖放Pickle文件到窗口中加载，或者从表格中拖出数据。')
        event.accept()
    
    def dropEvent(self, event: QDropEvent):
        """处理拖放事件"""
        self.handle_drop_event(event)
    
    def handle_drop_event(self, event: QDropEvent):
        """统一处理拖放事件"""
        if not event.mimeData().hasUrls():
            event.ignore()
            return
            
        urls = event.mimeData().urls()
        if not urls:
            event.ignore()
            return
            
        file_path = urls[0].toLocalFile()
        if not file_path.endswith(('.pkl', '.pickle')):
            self.statusBar().showMessage(f'不支持的文件类型: {os.path.basename(file_path)}', 5000)
            event.ignore()
            return
            
        self.load_pickle_file(file_path)
        # 在状态栏显示已成功导入文件的消息
        self.statusBar().showMessage(f'已成功导入文件 — {os.path.basename(file_path)}', 5000)
        event.accept()
        event.acceptProposedAction()
    
    def open_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, '选择Pickle文件', '', 'Pickle文件 (*.pkl *.pickle)')
        
        if file_path:
            self.load_pickle_file(file_path)
    
    def load_pickle_file(self, file_path):
        """异步加载pickle文件"""
        # 创建进度对话框
        self.progress_dialog = QProgressDialog("加载Pickle文件...", "取消", 0, 100, self)
        self.progress_dialog.setWindowTitle("加载中")
        self.progress_dialog.setWindowModality(Qt.WindowModal)
        self.progress_dialog.setMinimumDuration(500)  # 显示对话框前的最小延迟时间(毫秒)
        self.progress_dialog.setAutoClose(True)
        
        # 创建并启动加载线程
        self.loader_thread = PickleLoaderThread(file_path)
        self.loader_thread.finished.connect(self.on_pickle_loaded)
        self.loader_thread.progress.connect(self.progress_dialog.setValue)
        self.loader_thread.start()
        
        # 显示进度对话框
        self.progress_dialog.show()
    
    def on_pickle_loaded(self, data, file_path_or_error, success):
        """当pickle文件加载完成时调用"""
        if success:
            self.pickle_data = data
            self.file_label.setText(file_path_or_error)
            self.populate_tree_lazy()
            # 在弹窗中显示已成功加载文件的消息
            QMessageBox.information(self, '成功', f'已成功加载Pickle文件: {os.path.basename(file_path_or_error)}')
            # 在状态栏显示已成功导入文件的消息
            self.statusBar().showMessage(f'已成功导入文件 — {os.path.basename(file_path_or_error)}', 5000)
        else:
            QMessageBox.critical(self, '错误', f'无法加载Pickle文件: {file_path_or_error}')
    
    def populate_tree_lazy(self):
        """使用延迟加载方式填充树状视图"""
        self.tree_widget.clear()
        
        if self.pickle_data is None:
            return
        
        # 处理顶层数据
        if isinstance(self.pickle_data, dict):
            # 如果是字典，添加所有键
            keys = list(self.pickle_data.keys())
            if len(keys) > MAX_TREE_ITEMS:
                # 如果键太多，只显示部分
                for key in keys[:MAX_TREE_ITEMS]:
                    LazyTreeItem(self.tree_widget, str(key), lambda k=key: self.pickle_data[k])
                self.tree_widget.addTopLevelItem(QTreeWidgetItem([f"... 还有 {len(keys) - MAX_TREE_ITEMS} 个键"]))
            else:
                for key in keys:
                    LazyTreeItem(self.tree_widget, str(key), lambda k=key: self.pickle_data[k])
        else:
            # 如果不是字典，添加一个根项
            root_item = LazyTreeItem(self.tree_widget, "数据", lambda: self.pickle_data, False)
            self.add_lazy_children(root_item, self.pickle_data)
        
        # 展开第一级
        self.tree_widget.expandToDepth(0)
    
    def add_lazy_children(self, parent_item, obj):
        """添加延迟加载的子项"""
        if isinstance(obj, dict):
            parent_item.load_dict(obj)
        elif isinstance(obj, (list, tuple, np.ndarray)):
            parent_item.load_sequence(obj)
        elif isinstance(obj, pd.DataFrame):
            parent_item.load_dataframe(obj)
        elif isinstance(obj, pd.Series):
            parent_item.load_series(obj)
        else:
            # 基本类型，直接显示值
            value_str = str(obj)
            if len(value_str) > MAX_PREVIEW_SIZE:
                value_str = value_str[:MAX_PREVIEW_SIZE] + "..."
            parent_item.addChild(QTreeWidgetItem([f"{type(obj).__name__}: {value_str}"]))
    
    def on_tree_item_expanded(self, item):
        """当树状项被展开时调用"""
        if isinstance(item, LazyTreeItem):
            item.load_children()
    
    def on_tree_item_clicked(self, item, column):
        """当树状视图中的项被点击时调用"""
        # 如果是LazyTreeItem，确保已加载
        if isinstance(item, LazyTreeItem):
            item.load_children()
            
        path = self.get_item_path(item)
        if not path:
            return
        
        # 获取选中项的数据
        data = self.get_data_from_path(path)
        if data is None:
            return
        
        # 显示数据
        self.display_data(data, path[-1] if path else "Root")
    
    def get_item_path(self, item):
        """获取树状项的完整路径"""
        path = []
        while item is not None:
            text = item.text(0)
            # 跳过信息性文本，如"大小: xxx行 x xxx列"
            if not (text.startswith("大小:") or text.startswith("...") or text == "加载中..."):
                path.insert(0, text)
            item = item.parent()
        return path
    
    def get_data_from_path(self, path):
        """根据路径获取数据"""
        if self.pickle_data is None or not path:
            return None
        
        # 如果顶层不是字典，而是使用了"数据"作为根节点
        if not isinstance(self.pickle_data, dict) and path[0] == "数据":
            current = self.pickle_data
            # 跳过第一个元素，因为它是人为添加的"数据"节点
            path = path[1:]
        else:
            current = self.pickle_data
        
        for key in path:
            if isinstance(current, dict) and key in current:
                current = current[key]
            elif isinstance(current, (list, tuple, np.ndarray)) and key.startswith('[') and key.endswith(']'):
                try:
                    index = int(key[1:-1])
                    if 0 <= index < len(current):
                        current = current[index]
                    else:
                        return None
                except ValueError:
                    return None
            elif isinstance(current, pd.DataFrame) and key == "columns":
                current = pd.Series(current.columns)
            elif isinstance(current, pd.DataFrame) and key == "data (preview)":
                current = current.head()
            elif isinstance(current, pd.Series) and key == "index":
                current = pd.Series(current.index)
            elif isinstance(current, pd.Series) and key == "values (preview)":
                current = current.head()
            else:
                # 处理特殊情况，如"行 X"
                if key.startswith("行 ") and isinstance(current, pd.DataFrame):
                    try:
                        row_idx = int(key.split(" ")[1])
                        if 0 <= row_idx < len(current):
                            current = current.iloc[row_idx]
                        else:
                            return None
                    except (ValueError, IndexError):
                        return None
                else:
                    return None
        
        return current
    
    def display_data(self, data, title):
        """在表格视图中显示数据"""
        # 清除现有表格
        self.normal_table.setModel(None)
        self.transposed_table.setModel(None)
        
        try:
            if isinstance(data, pd.DataFrame):
                # 检查DataFrame大小
                if data.shape[0] * data.shape[1] > LARGE_DF_THRESHOLD:
                    # 显示提示对话框
                    reply = QMessageBox.question(self, '大型数据警告', 
                        f'数据较大 ({data.shape[0]}行 x {data.shape[1]}列)，显示可能会很慢。是否继续?',
                        QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                    
                    if reply == QMessageBox.No:
                        return
                
                # 设置普通视图
                normal_model = PandasModel(data)
                self.normal_table.setModel(normal_model)
                
                # 设置转置视图
                # 对于大型DataFrame，可能不需要显示转置视图
                if data.shape[0] * data.shape[1] > LARGE_DF_THRESHOLD:
                    self.tab_widget.setTabEnabled(1, False)
                    self.tab_widget.setTabText(1, f'转置视图 - 已禁用(数据过大)')
                else:
                    transposed_model = PandasModel(data.T)
                    self.transposed_table.setModel(transposed_model)
                    self.tab_widget.setTabEnabled(1, True)
                    self.tab_widget.setTabText(1, f'转置视图 - {title}')
                
                # 更新标签
                self.tab_widget.setTabText(0, f'普通视图 - {title}')
                
            elif isinstance(data, pd.Series):
                # 将Series转换为DataFrame
                df = pd.DataFrame(data)
                
                # 设置普通视图
                normal_model = PandasModel(df)
                self.normal_table.setModel(normal_model)
                
                # 设置转置视图
                transposed_model = PandasModel(df.T)
                self.transposed_table.setModel(transposed_model)
                self.tab_widget.setTabEnabled(1, True)
                
                # 更新标签
                self.tab_widget.setTabText(0, f'普通视图 - {title}')
                self.tab_widget.setTabText(1, f'转置视图 - {title}')
                
            elif isinstance(data, dict):
                # 转换为DataFrame
                df = pd.DataFrame.from_dict(data, orient='index')
                
                # 设置普通视图
                normal_model = PandasModel(df)
                self.normal_table.setModel(normal_model)
                
                # 设置转置视图
                transposed_model = PandasModel(df.T)
                self.transposed_table.setModel(transposed_model)
                self.tab_widget.setTabEnabled(1, True)
                
                # 更新标签
                self.tab_widget.setTabText(0, f'普通视图 - {title}')
                self.tab_widget.setTabText(1, f'转置视图 - {title}')
                
            elif isinstance(data, (list, tuple, np.ndarray)):
                # 尝试转换为DataFrame
                try:
                    # 检查数据大小
                    if len(data) > LARGE_DF_THRESHOLD:
                        reply = QMessageBox.question(self, '大型数据警告', 
                            f'数据较大 ({len(data)}个元素)，显示可能会很慢。是否继续?',
                            QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
                        
                        if reply == QMessageBox.No:
                            return
                    
                    if all(isinstance(item, dict) for item in data):
                        # 列表中的所有项都是字典
                        df = pd.DataFrame(data)
                    else:
                        # 其他类型的列表
                        df = pd.DataFrame(data)
                    
                    # 设置普通视图
                    normal_model = PandasModel(df)
                    self.normal_table.setModel(normal_model)
                    
                    # 设置转置视图
                    if len(data) > LARGE_DF_THRESHOLD:
                        self.tab_widget.setTabEnabled(1, False)
                        self.tab_widget.setTabText(1, f'转置视图 - 已禁用(数据过大)')
                    else:
                        transposed_model = PandasModel(df.T)
                        self.transposed_table.setModel(transposed_model)
                        self.tab_widget.setTabEnabled(1, True)
                        self.tab_widget.setTabText(1, f'转置视图 - {title}')
                    
                    # 更新标签
                    self.tab_widget.setTabText(0, f'普通视图 - {title}')
                except Exception as e:
                    QMessageBox.information(self, '信息', f'无法将数据转换为表格: {str(e)}')
            else:
                # 不是可以直接转换为表格的数据类型
                QMessageBox.information(self, '信息', f'所选项 "{title}" 不能以表格形式显示。\n\n类型: {type(data).__name__}')
        except Exception as e:
            QMessageBox.information(self, '错误', f'显示数据时出错: {str(e)}')


if __name__ == '__main__':
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion风格，在所有平台上看起来都不错
    
    window = PickleViewerApp()
    window.show()
    
    sys.exit(app.exec_()) 