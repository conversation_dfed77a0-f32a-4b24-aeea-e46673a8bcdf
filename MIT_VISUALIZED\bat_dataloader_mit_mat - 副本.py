#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文件名: bat_dataloader_mit_mat.py
描述: 用于加载和处理MIT_mat格式的电池数据文件，提取恒压阶段数据
      更新：使用更严格的电压范围条件(±0.002V)和精确的电流阈值(1.0±0.002A)，
      简化恒压阶段识别逻辑，确保恒流阶段点最多只有1个
作者: Claude
日期: 2023-07-10
版本: 1.3
"""

import os                      # 操作系统接口，用于文件路径处理
import numpy as np             # 科学计算库，用于数值数组处理
import pandas as pd            # 数据分析库，用于表格数据处理
import pickle                  # Python对象序列化，用于保存和加载处理后的数据
import json                    # JSON数据处理，用于读取和写入JSON格式电池数据
import h5py                    # HDF5文件格式接口，用于处理大型数据集
import glob                    # 文件路径模式匹配，用于批量查找数据文件
import re                      # 正则表达式，用于文件名和数据解析
from scipy.io import loadmat   # 用于加载MATLAB格式的电池数据文件
from typing import Dict, List, Tuple, Union, Any, Optional  # 类型提示，增强代码可读性
import logging                 # 日志记录，用于跟踪数据加载和处理过程
import traceback               # 用于获取详细的错误信息

# 导入自定义模块
try:
    # 尝试以包内模块方式导入CVExtractor类（当作为包的一部分导入时使用这种方式）
    from .cv_extractor_mit_mat import CVExtractor
except ImportError:
    # 如果上面的导入失败，则尝试直接导入（当直接运行此脚本时使用这种方式）
    from cv_extractor_mit_mat import CVExtractor

# 设置日志配置
logging.basicConfig(
    level=logging.INFO,  # 设置日志级别为INFO，会显示INFO、WARNING、ERROR、CRITICAL级别的日志
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'  # 设置日志格式：时间-名称-级别-消息
)
# 创建一个名为'bat_dataloader'的日志记录器，用于记录此模块的日志信息
logger = logging.getLogger('bat_dataloader')

class BatteryDataLoader:
    """
    电池数据加载器类
    只用于MIT_mat数据集
    """
    
    def __init__(self, data_dir: str, cache_dir: str = None, voltage_threshold: float = 3.6, 
                 tolerance: float = 0.03, process_all_cells: bool = True,
                 current_rate_threshold: float = 0.05, strict_voltage_range: bool = True):
        """
        初始化电池数据加载器
        
        参数:
            data_dir (str): 数据目录
            cache_dir (str, 可选): 缓存目录
            voltage_threshold (float): 恒压阶段的电压阈值
            tolerance (float): 电压波动容差
            process_all_cells (bool): 是否处理.mat文件中的所有电池
            current_rate_threshold (float): 电流变化率阈值，用于区分恒流和恒压阶段
            strict_voltage_range (bool): 是否使用严格的电压范围条件(±0.002V)
        """
        self.data_dir = data_dir
        self.cache_dir = cache_dir or os.path.join(data_dir, 'cache')
        self.voltage_threshold = voltage_threshold
        self.tolerance = tolerance
        self.process_all_cells = process_all_cells
        self.current_rate_threshold = current_rate_threshold
        self.strict_voltage_range = strict_voltage_range
        
        # 创建缓存目录
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # 支持的文件扩展名
        self.supported_extensions = ['.mat']
        
        # 初始化恒压阶段提取器
        self.cv_extractor = CVExtractor(
            voltage_threshold=voltage_threshold,
            tolerance=0.002 if strict_voltage_range else tolerance,  # 使用更严格的电压范围
            min_duration=2.0,
            min_points=5,
            current_min_threshold=0.01,
            current_rate_threshold=current_rate_threshold
        )
        
        logger.info(f"初始化MIT_mat电池数据加载器，数据目录: {data_dir}")
        logger.info(f"恒压阈值: {voltage_threshold}V，容差: {0.002 if strict_voltage_range else tolerance}V")
        logger.info(f"处理所有电池: {process_all_cells}")
        logger.info(f"电流变化率阈值: {current_rate_threshold}")
        logger.info(f"严格电压范围模式: {strict_voltage_range}")
        logger.info(f"缓存目录: {self.cache_dir}")
    
    def get_file_list(self) -> List[str]:
        """
        获取数据目录中的MIT_mat文件列表
        
        返回:
            List[str]: 文件路径列表
        """
        return glob.glob(os.path.join(self.data_dir, "**/*.mat"), recursive=True)
    
    def load_mat_file(self, file_path: str) -> Dict[str, Any]:
        """
        加载.mat文件，处理MIT数据集的格式
        """
        try:
            # 使用h5py加载
            with h5py.File(file_path, 'r') as f:
                data = {}
                # 检查是否有batch键（MIT数据集格式）
                if 'batch' in f:
                    print(f"找到batch键，正在处理MIT格式MAT文件")
                    batch = f['batch']
                    batch_keys = list(batch.keys())
                    print(f"batch键包含: {batch_keys}")
                    
                    # 提取batch中的数据
                    data['batch'] = {
                        'keys': batch_keys
                    }
                    
                    # 处理summary数据
                    if 'summary' in batch.keys():
                        data['batch']['summary_shape'] = batch['summary'].shape
                    
                    # 处理cycles数据
                    if 'cycles' in batch.keys():
                        data['batch']['cycles_shape'] = batch['cycles'].shape
                    
                    # 处理cycle_life数据
                    if 'cycle_life' in batch.keys():
                        data['batch']['cycle_life_shape'] = batch['cycle_life'].shape
                    
                    # 处理policy_readable数据
                    if 'policy_readable' in batch.keys():
                        data['batch']['policy_readable_shape'] = batch['policy_readable'].shape
                
                # 保存文件路径信息
                data['file_path'] = file_path
                
            logger.info(f"使用h5py成功加载文件: {file_path}")
            return data
        except Exception as e:
            logger.error(f"加载.mat文件时出错: {e}")
            return {}
    
    def _process_mit_mat_data(self, data: Dict, file_path: str) -> Dict[str, Dict[str, Any]]:
        """
        处理MIT数据集的MAT格式数据
        
        参数:
            data (Dict): 加载的数据
            file_path (str): 文件路径
            
        返回:
            Dict[str, Dict[str, Any]]: 处理后的多个电池数据，格式为{电池ID: 电池数据}
        """
        # 提取文件名作为基础ID
        base_id = os.path.basename(file_path).split('.')[0]
        
        # 创建返回的电池数据字典
        all_batteries = {}
        
        logger.info(f"处理MIT MAT数据，文件: {base_id}，使用恒压阈值: {self.voltage_threshold}V")
        
        try:
            # 重新打开文件以正确处理数据
            with h5py.File(file_path, 'r') as f:
                logger.debug(f"H5PY文件键: {list(f.keys())}")
                
                # 检查是否存在batch键
                if 'batch' in f:
                    batch = f["batch"]
                    logger.debug(f"Batch键: {list(batch.keys())}")
                    
                    # 获取电池数量
                    num_cells = batch["summary"].shape[0] if 'summary' in batch else 0
                    logger.info(f"发现 {num_cells} 个电池")
                    
                    # 如果不处理所有电池，则只处理第一个电池
                    if not self.process_all_cells:
                        logger.info("设置为只处理第一个电池")
                        num_cells = min(1, num_cells)
                    
                    # 创建电池字典
                    bat_dict = {}
                    
                    # 处理每个电池
                    for i in range(num_cells):
                        logger.info(f"处理电池 {i+1}/{num_cells}")
                        
                        # 提取循环寿命数据
                        cl = f[batch['cycle_life'][i, 0]][:]
                        # 添加对NaN值的检查，避免转换错误
                        if cl.size > 0 and not np.isnan(cl[0, 0]):
                            cycle_life = int(cl[0, 0])
                        else:
                            cycle_life = None
                        logger.debug(f"循环寿命: {cycle_life}")
                        
                        # 提取充电策略
                        try:
                            policy = f[batch['policy_readable'][i, 0]][:].tobytes()[::2].decode()
                            logger.debug(f"充电策略: {policy}")
                        except Exception as e:
                            policy = "未知"
                            logger.warning(f"解析充电策略时出错: {e}")
                        
                        # 尝试提取电池编号（如果存在）
                        battery_number = None
                        try:
                            if 'barcode' in batch:
                                barcode_ref = batch['barcode'][i, 0]
                                if barcode_ref is not None:
                                    barcode_bytes = f[barcode_ref][:]
                                    if barcode_bytes.size > 0:
                                        # 尝试解码，并确保移除任何空字符和不可打印字符
                                        try:
                                            barcode = barcode_bytes.tobytes()[::2].decode('utf-8', errors='ignore')
                                            # 过滤掉非打印字符和空字符
                                            barcode = ''.join(c for c in barcode if c.isprintable() and c != '\0')
                                            if barcode:
                                                battery_number = barcode
                                                logger.debug(f"提取到电池编号: {battery_number}")
                                        except Exception as e:
                                            logger.warning(f"解码barcode时出错: {e}")
                        except Exception as e:
                            logger.warning(f"提取电池编号时出错: {e}")
                            battery_number = None
                        
                        # 提取summary数据
                        summary_obj = f[batch['summary'][i, 0]]
                        logger.debug(f"Summary对象键: {list(summary_obj.keys())}")
                        
                        # 提取各种summary数据
                        try:
                            summary_IR = np.hstack(f[summary_obj['IR'][0, :].tolist()])
                            summary_QC = np.hstack(f[summary_obj['QCharge'][0, :].tolist()])
                            summary_QD = np.hstack(f[summary_obj['QDischarge'][0, :].tolist()])
                            summary_TA = np.hstack(f[summary_obj['Tavg'][0, :].tolist()])
                            summary_TM = np.hstack(f[summary_obj['Tmin'][0, :].tolist()])
                            summary_TX = np.hstack(f[summary_obj['Tmax'][0, :].tolist()])
                            summary_CT = np.hstack(f[summary_obj['chargetime'][0, :].tolist()])
                            summary_CY = np.hstack(f[summary_obj['cycle'][0, :].tolist()])
                            
                            summary = {
                                'IR': summary_IR, 
                                'QC': summary_QC, 
                                'QD': summary_QD, 
                                'Tavg': summary_TA, 
                                'Tmin': summary_TM, 
                                'Tmax': summary_TX, 
                                'chargetime': summary_CT,
                                'cycle': summary_CY
                            }
                            
                            logger.debug("成功提取summary数据")
                        except Exception as e:
                            error_details = traceback.format_exc()
                            logger.error(f"提取summary数据时出错: {e}\n{error_details}")
                            summary = {}
                        
                        # 提取cycles数据
                        cycles_obj = f[batch['cycles'][i, 0]]
                        logger.debug(f"Cycles对象键: {list(cycles_obj.keys())}")
                        
                        # 处理每个循环
                        cycle_count = cycles_obj['I'].shape[0] if 'I' in cycles_obj else 0
                        logger.info(f"循环数量: {cycle_count}")
                        
                        # 创建当前电池的处理数据
                        # 使用文件名、电池编号（如果有）和索引创建唯一的电池ID
                        if battery_number:
                            # 确保电池编号中不包含无效字符
                            safe_battery_number = ''.join(c for c in battery_number if c.isalnum() or c in '-_.')
                            if safe_battery_number:
                                battery_id = f"{base_id}_b{safe_battery_number}"
                            else:
                                battery_id = f"{base_id}_cell_{i+1}"
                        else:
                            battery_id = f"{base_id}_cell_{i+1}"
                        
                        # 最后检查确保ID不包含无效字符
                        battery_id = ''.join(c for c in battery_id if c.isprintable() and c != '\0' and c != ' ')
                        logger.info(f"生成电池ID: {battery_id}")
                        
                        processed_data = {
                            'battery_id': battery_id,
                            'dataset': 'MIT_mat',
                            'cycle_life': cycle_life,
                            'charge_policy': policy,
                            'summary': summary,
                            'cycles': {}
                        }
                        
                        cycle_dict = {}
                        for j in range(cycle_count):
                            # 每隔100个循环打印一次进度和详细信息
                            if j % 100 == 0:
                                logger.info(f"处理循环 {j+1}/{cycle_count}")
                            
                            try:
                                # 提取所有循环数据，完全匹配参考代码方式
                                I = np.hstack((f[cycles_obj['I'][j, 0]][:]))
                                Qc = np.hstack((f[cycles_obj['Qc'][j, 0]][:]))
                                Qd = np.hstack((f[cycles_obj['Qd'][j, 0]][:]))
                                Qdlin = np.hstack((f[cycles_obj['Qdlin'][j, 0]][:]))
                                T = np.hstack((f[cycles_obj['T'][j, 0]][:]))
                                Tdlin = np.hstack((f[cycles_obj['Tdlin'][j, 0]][:]))
                                V = np.hstack((f[cycles_obj['V'][j, 0]][:]))
                                dQdV = np.hstack((f[cycles_obj['discharge_dQdV'][j, 0]][:]))
                                t = np.hstack((f[cycles_obj['t'][j, 0]][:]))
                                
                                cycle_data = {
                                    'I': I, 
                                    'Qc': Qc, 
                                    'Qd': Qd, 
                                    'Qdlin': Qdlin, 
                                    'T': T, 
                                    'Tdlin': Tdlin, 
                                    'V': V, 
                                    'dQdV': dQdV, 
                                    't': t
                                }
                                
                                # 添加到循环字典
                                cycle_dict[str(j)] = cycle_data
                                
                                # 提取恒压阶段数据
                                if len(V) > 0 and len(I) > 0 and len(t) > 0:
                                    # 提取恒压阶段数据
                                    cv_data = self._extract_cv_phase_data(t, V, I)
                                    
                                    if cv_data:
                                        # 每隔100个循环打印一次详细信息
                                        if j % 100 == 0:
                                            cv_time = cv_data['time']
                                            cv_voltage = cv_data['voltage']
                                            cv_current = cv_data['current']
                                            duration = cv_time[-1] - cv_time[0]
                                            
                                            logger.info(f"循环 {j+1} 恒压段详细信息:")
                                            logger.info(f"  点数: {len(cv_time)}")
                                            logger.info(f"  电压范围: {min(cv_voltage):.4f}V - {max(cv_voltage):.4f}V, 均值: {np.mean(cv_voltage):.4f}V")
                                            logger.info(f"  电流范围: {min(cv_current):.4f}A - {max(cv_current):.4f}A, 均值: {np.mean(cv_current):.4f}A")
                                            logger.info(f"  开始时间点: {cv_time[0]:.2f}分钟, 结束时间点: {cv_time[-1]:.2f}分钟")
                                            logger.info(f"  首末电流值: 开始={cv_current[0]:.4f}A, 结束={cv_current[-1]:.4f}A")
                                            logger.info(f"  电压初值: {cv_voltage[0]:.4f}V, 电压末值: {cv_voltage[-1]:.4f}V")
                                            logger.info(f"  持续时间: {duration:.2f}分钟")
                                        
                                        processed_data['cycles'][j+1] = {
                                            'charge': {
                                                'time': t,
                                                'voltage': V,
                                                'current': I,
                                                'capacity': Qc[-1] if len(Qc) > 0 else None
                                            },
                                            'cv_phase': cv_data
                                        }
                            except Exception as e:
                                error_details = traceback.format_exc()
                                logger.error(f"提取循环 {j+1} 数据时出错: {e}\n{error_details}")
                        
                        # 创建电池完整信息
                        cell_dict = {
                            'cycle_life': cl, 
                            'charge_policy': policy, 
                            'summary': summary, 
                            'cycles': cycle_dict
                        }
                        
                        # 添加到电池字典
                        key = f'b3c{i}'
                        bat_dict[key] = cell_dict
                        
                        # 将处理后的数据添加到返回字典中
                        all_batteries[battery_id] = processed_data
                    
                    # 可选：保存为pkl文件
                    save_name = f'{base_id}_processed.pkl'
                    save_path = os.path.join(self.cache_dir, save_name)
                    with open(save_path, 'wb') as fp:
                        pickle.dump(bat_dict, fp)
                    logger.info(f"已保存处理后的数据到: {save_path}")
                    
        except Exception as e:
            error_details = traceback.format_exc()
            logger.error(f"处理MIT MAT文件时出错: {e}\n{error_details}")
        
        logger.info(f"处理完成，共提取 {len(all_batteries)} 个电池数据")
        return all_batteries
    
    def _extract_cv_phase_data(self, time, voltage, current):
        """
        提取恒压阶段数据的方法，专为MIT数据优化
        
        参数:
            time (np.ndarray): 时间数据
            voltage (np.ndarray): 电压数据
            current (np.ndarray): 电流数据
            
        返回:
            Dict: 恒压阶段数据
        """
        if len(voltage) == 0 or len(current) == 0 or len(time) == 0:
            return None
        
        # 获取恒压提取器的参数
        voltage_threshold = self.cv_extractor.voltage_threshold
        tolerance = self.cv_extractor.tolerance
        current_min_threshold = self.cv_extractor.current_min_threshold
        
        # 使用恒压提取器提取所有恒压阶段
        cv_phases = self.cv_extractor.extract_all_cv_phases(time, voltage, current)
        if not cv_phases:
            return None
        
        # 寻找最长的恒压阶段
        max_duration, max_phase_idx = 0, 0
        for i, phase in enumerate(cv_phases):
            # 忽略电流接近0的弛豫阶段
            valid_points = np.sum(np.abs(phase['current']) > current_min_threshold)
            if valid_points < self.cv_extractor.min_points:
                continue
                
            duration = phase['time'][-1] - phase['time'][0]
            if duration > max_duration:
                max_duration = duration
                max_phase_idx = i
                
        # 提取最长的恒压阶段
        cv_phase = cv_phases[max_phase_idx]
        cv_time = cv_phase['time']
        cv_voltage = cv_phase['voltage']
        cv_current = cv_phase['current']
        
        return {
            'time': cv_time,
            'voltage': cv_voltage,
            'current': cv_current
        }
    
    def load_all_batteries(self, max_batteries: Optional[int] = None) -> Dict[str, Dict[str, Any]]:
        """
        加载所有MIT_mat电池数据
        
        参数:
            max_batteries (int, 可选): 最多加载的电池数量
            
        返回:
            Dict[str, Dict[str, Any]]: 电池数据字典，格式为{电池ID: 电池数据}
        """
        # 获取所有电池文件
        battery_files = self.get_file_list()
        
        if not battery_files:
            logger.warning(f"在{self.data_dir}中未找到MIT_mat电池数据文件")
            return {}
        
        # 如果指定了最大电池数量，则限制加载的电池数量
        if max_batteries is not None:
            battery_files = battery_files[:max_batteries]
        
        logger.info(f"找到{len(battery_files)}个MIT_mat电池数据文件")
        
        # 加载所有电池数据
        all_batteries = {}
        
        for file_path in battery_files:
            file_name = os.path.basename(file_path)
            logger.info(f"处理文件: {file_name}")
            
            # 加载MAT文件
            data = self.load_mat_file(file_path)
            
            # 处理MIT数据集，返回多个电池数据
            battery_dict = self._process_mit_mat_data(data, file_path)
            
            # 将所有电池数据添加到总字典中
            all_batteries.update(battery_dict)
            logger.info(f"从文件 {file_name} 中提取了 {len(battery_dict)} 个电池数据")
        
        logger.info(f"成功加载{len(all_batteries)}个电池数据")
        return all_batteries

if __name__ == "__main__":
    # 示例用法
    data_dir = "E:\\SOH+LP_rawdata_MIT_HUST_XJTU_TJU\\MIT_rawdata_mat"
    loader = BatteryDataLoader(data_dir, voltage_threshold=3.6, tolerance=0.03)
    
    # 只测试MIT_mat数据集
    logger.info("\n===== 测试MIT_mat数据集 =====")
    mit_mat_batteries = loader.load_all_batteries(max_batteries=1)
    logger.info(f"加载了 {len(mit_mat_batteries)} 个MIT_mat电池") 