#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文件名: process_mit_mat.py
描述: 处理MIT_mat数据集，提取CV阶段数据并按循环存储为JSON文件
      更新：使用更严格的电压范围条件(±0.002V)和精确的电流阈值(1.0±0.002A)，
      简化恒压阶段识别逻辑，确保恒流阶段点最多只有1个
作者: Claude
日期: 2023-07-10
版本: 1.3
"""

import os
import json
import numpy as np
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional
import matplotlib.pyplot as plt
from bat_dataloader_mit_mat import BatteryDataLoader
import time

# 设置日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('process_mit_mat')

def process_dataset(dataset_path: str, output_dir: str, 
                   max_batteries: Optional[int] = None, 
                   separate_cycles: bool = False,
                   save_combined: bool = True,
                   info_dir: Optional[str] = None,
                   group_by_policy: bool = False,
                   process_all_cells: bool = True,
                   current_rate_threshold: float = 0.05,
                   strict_voltage_range: bool = True) -> None:
    """
    处理MIT_mat数据集，提取CV阶段数据并保存为JSON
    
    参数:
        dataset_path (str): MIT_mat数据集路径
        output_dir (str): 输出目录
        max_batteries (int, 可选): 最多处理的电池数量
        separate_cycles (bool): 是否将每个循环的CV数据保存为单独的文件
        save_combined (bool): 是否保存合并的CV数据文件
        info_dir (str, 可选): CV信息输出目录，默认与output_dir相同
        group_by_policy (bool): 是否按充电策略分组保存
        process_all_cells (bool): 是否处理.mat文件中的所有电池，默认为True
        current_rate_threshold (float): 电流变化率阈值，用于区分恒流和恒压阶段
        strict_voltage_range (bool): 是否使用严格的电压范围条件(±0.002V)
    """
    logger.info(f"开始处理MIT_mat数据集...")
    start_time = time.time()
    
    # 创建数据集输出目录
    dataset_output_dir = os.path.join(output_dir, "MIT_mat")
    os.makedirs(dataset_output_dir, exist_ok=True)
    
    # 创建数据集CV信息输出目录
    if info_dir:
        dataset_info_dir = os.path.join(info_dir, "MIT_mat")
        os.makedirs(dataset_info_dir, exist_ok=True)
    else:
        dataset_info_dir = dataset_output_dir
    
    # 创建电池数据加载器
    # MIT_mat数据集的恒压阈值为3.6V
    voltage_threshold = 3.6
    
    # 设置处理所有电池的标志
    if process_all_cells:
        logger.info(f"将处理MIT_mat数据集中每个.mat文件的所有电池")
    
    # 创建加载器
    loader = BatteryDataLoader(
        data_dir=dataset_path,
        cache_dir=os.path.join(dataset_output_dir, 'cache'),
        voltage_threshold=voltage_threshold,
        tolerance=0.03,  # 增大容差以适应实际数据波动
        process_all_cells=process_all_cells,
        current_rate_threshold=current_rate_threshold,
        strict_voltage_range=strict_voltage_range
    )
    
    # 加载所有电池数据
    battery_data = loader.load_all_batteries(max_batteries=max_batteries)
    
    if not battery_data:
        logger.warning(f"MIT_mat数据集未找到有效电池数据")
        return
    
    # 统计不同充电策略的电池数量
    policy_counts = {}
    for battery_id, data in battery_data.items():
        policy = data.get('charge_policy', 'unknown')
        if policy not in policy_counts:
            policy_counts[policy] = 0
        policy_counts[policy] += 1
    
    logger.info(f"成功加载MIT_mat数据集，共{len(battery_data)}个电池，按充电策略分布:")
    for policy, count in policy_counts.items():
        logger.info(f"  策略 '{policy}': {count}个电池")
    
    # 如果按充电策略分组，先收集所有电池的充电策略
    if group_by_policy:
        policy_groups = {}
        for battery_id, data in battery_data.items():
            charge_policy = data.get('charge_policy', 'unknown')
            
            # 确保充电策略不包含无效字符
            safe_charge_policy = ''.join(c for c in charge_policy if c.isprintable() and c != '\0' and c != ' ')
            if safe_charge_policy != charge_policy:
                logger.warning(f"充电策略包含无效字符，已修正: '{charge_policy}' -> '{safe_charge_policy}'")
                charge_policy = safe_charge_policy
                # 更新电池数据中的充电策略
                data['charge_policy'] = charge_policy
            
            if charge_policy not in policy_groups:
                policy_groups[charge_policy] = []
            policy_groups[charge_policy].append((battery_id, data))
        
        # 为每个充电策略创建目录并处理电池
        for policy, batteries in policy_groups.items():
            # 确保策略名称不包含无效字符
            safe_policy = ''.join(c for c in policy if c.isprintable() and c != '\0' and c != ' ')
            if safe_policy != policy:
                logger.warning(f"充电策略目录名包含无效字符，已修正: '{policy}' -> '{safe_policy}'")
            
            # 创建充电策略目录
            policy_dir = os.path.join(dataset_output_dir, f"policy_{safe_policy}")
            os.makedirs(policy_dir, exist_ok=True)
            
            # 创建充电策略CV信息目录
            policy_info_dir = os.path.join(dataset_info_dir, f"policy_{safe_policy}")
            os.makedirs(policy_info_dir, exist_ok=True)
            
            logger.info(f"处理充电策略 '{policy}' 的电池，共 {len(batteries)} 个")
            
            # 处理每个电池
            for battery_id, data in batteries:
                process_battery(battery_id, data, policy_dir, separate_cycles, save_combined, policy_info_dir, policy)
    else:
        # 处理每个电池的数据，不按充电策略分组
        for battery_id, data in battery_data.items():
            process_battery(battery_id, data, dataset_output_dir, separate_cycles, save_combined, dataset_info_dir)
    
    end_time = time.time()
    logger.info(f"MIT_mat数据集处理完成，共处理{len(battery_data)}个电池，耗时: {end_time - start_time:.2f}秒")

def process_battery(battery_id: str, battery_data: Dict[str, Any], output_dir: str, 
                   separate_cycles: bool = False, save_combined: bool = True,
                   info_dir: Optional[str] = None, charge_policy: Optional[str] = None) -> None:
    """
    处理单个电池的数据，提取CV阶段并保存为JSON
    
    参数:
        battery_id (str): 电池ID
        battery_data (Dict[str, Any]): 电池数据
        output_dir (str): 输出目录
        separate_cycles (bool): 是否将每个循环的CV数据保存为单独的文件
        save_combined (bool): 是否保存合并的CV数据文件
        info_dir (str, 可选): CV信息输出目录，默认与output_dir相同
        charge_policy (str, 可选): 充电策略，用于文件命名
    """
    # 确保电池ID不包含无效字符
    safe_battery_id = ''.join(c for c in battery_id if c.isprintable() and c != '\0' and c != ' ')
    if safe_battery_id != battery_id:
        logger.warning(f"电池ID包含无效字符，已修正: '{battery_id}' -> '{safe_battery_id}'")
        battery_id = safe_battery_id
    
    # 获取电池的充电策略
    battery_charge_policy = charge_policy or battery_data.get('charge_policy', 'unknown')
    
    # 确保充电策略不包含无效字符
    safe_charge_policy = ''.join(c for c in battery_charge_policy if c.isprintable() and c != '\0' and c != ' ')
    if safe_charge_policy != battery_charge_policy:
        logger.warning(f"充电策略包含无效字符，已修正: '{battery_charge_policy}' -> '{safe_charge_policy}'")
        battery_charge_policy = safe_charge_policy
    
    # 获取电池的循环寿命
    cycle_life = battery_data.get('cycle_life')
    cycle_life_str = f"循环寿命: {cycle_life}" if cycle_life else "循环寿命: 未知"
    
    logger.info(f"处理电池 {battery_id}，充电策略: {battery_charge_policy}，{cycle_life_str}...")
    start_time = time.time()
    
    # 创建电池输出目录
    battery_output_dir = os.path.join(output_dir, battery_id)
    os.makedirs(battery_output_dir, exist_ok=True)
    
    # 创建电池CV信息输出目录
    if info_dir:
        battery_info_dir = os.path.join(info_dir, battery_id)
        os.makedirs(battery_info_dir, exist_ok=True)
    else:
        battery_info_dir = battery_output_dir
    
    # 如果需要分循环保存，创建循环数据目录
    cycles_dir = os.path.join(battery_output_dir, 'cycles')
    if separate_cycles:
        os.makedirs(cycles_dir, exist_ok=True)
    
    # 获取循环数据
    cycles = battery_data.get('cycles', {})
    
    if not cycles:
        logger.warning(f"电池 {battery_id} 没有循环数据")
        return
    
    logger.info(f"电池 {battery_id} 有 {len(cycles)} 个循环")
    
    # 创建文件名前缀，包含电池ID和充电策略
    file_prefix = f"{safe_battery_id}_policy_{safe_charge_policy}"
    
    # 创建CV信息文本文件，使用充电策略命名
    cv_info_file = os.path.join(battery_info_dir, f'{file_prefix}_cv_info.txt')
    with open(cv_info_file, 'w') as info_f:
        info_f.write(f"电池ID: {battery_id}\n")
        info_f.write(f"数据集: {battery_data.get('dataset', 'MIT_mat')}\n")
        info_f.write(f"总循环数: {len(cycles)}\n")
        info_f.write(f"循环寿命: {cycle_life}\n")
        info_f.write(f"充电策略: {battery_charge_policy}\n\n")
        info_f.write("=" * 80 + "\n\n")
    
    # 提取CV阶段数据并保存为JSON
    cv_phases_data = {}
    cv_summary = {
        'battery_id': battery_id,
        'dataset': 'MIT_mat',
        'total_cycles': len(cycles),
        'cycles_with_cv': 0,
        'cv_stats': {
            'durations': [],
            'point_counts': [],
            'voltage_means': [],
            'current_means': [],
            'end_currents': []
        },
        'cycle_life': cycle_life,
        'charge_policy': battery_charge_policy
    }
    
    # 处理每个循环
    for cycle_number, cycle_data in cycles.items():
        cv_data = cycle_data.get('cv_phase')
        if cv_data:
            # 将NumPy数组转换为列表以便JSON序列化
            cycle_cv_data = {
                'time': cv_data['time'].tolist(),
                'voltage': cv_data['voltage'].tolist(),
                'current': cv_data['current'].tolist(),
                'cycle': int(cycle_number),
                'sequence_number': int(cycle_number),  # 添加序号信息
                'charge_policy': battery_charge_policy  # 添加充电策略信息
            }
            
            # 如果有容量数据，添加到CV数据中
            if 'charge' in cycle_data and 'capacity' in cycle_data['charge'] and cycle_data['charge']['capacity'] is not None:
                cycle_cv_data['capacity'] = float(cycle_data['charge']['capacity'])
            
            # 添加到CV阶段数据字典
            cv_phases_data[int(cycle_number)] = cycle_cv_data
            
            # 更新统计信息
            cv_time = cv_data['time']
            cv_voltage = cv_data['voltage']
            cv_current = cv_data['current']
            
            duration = cv_time[-1] - cv_time[0]
            cv_summary['cv_stats']['durations'].append(float(duration))
            cv_summary['cv_stats']['point_counts'].append(len(cv_time))
            cv_summary['cv_stats']['voltage_means'].append(float(np.mean(cv_voltage)))
            cv_summary['cv_stats']['current_means'].append(float(np.mean(cv_current)))
            cv_summary['cv_stats']['end_currents'].append(float(cv_current[-1]))
            
            # 将CV阶段信息写入文本文件
            with open(cv_info_file, 'a') as info_f:
                info_f.write(f"循环 {int(cycle_number)} CV阶段信息:\n")
                info_f.write(f"  点数: {len(cv_time)}\n")
                info_f.write(f"  持续时间: {duration:.2f}分钟\n")
                info_f.write(f"  电压范围: {min(cv_voltage):.4f}V - {max(cv_voltage):.4f}V, 均值: {float(np.mean(cv_voltage)):.4f}V\n")
                info_f.write(f"  电流范围: {min(cv_current):.4f}A - {max(cv_current):.4f}A, 均值: {float(np.mean(cv_current)):.4f}A\n")
                info_f.write(f"  首末电流值: 开始={cv_current[0]:.4f}A, 结束={cv_current[-1]:.4f}A\n")
                info_f.write(f"  电压初值: {cv_voltage[0]:.4f}V, 电压末值: {cv_voltage[-1]:.4f}V\n\n")
                
                # 打印CV阶段初始三个点
                info_f.write("  CV阶段初始三个点:\n")
                for i in range(min(3, len(cv_time))):
                    info_f.write(f"    点{i+1}: 时间={cv_time[i]:.2f}分钟, 电压={cv_voltage[i]:.4f}V, 电流={cv_current[i]:.4f}A\n")
                
                # 打印CV阶段末尾三个点
                info_f.write("  CV阶段末尾三个点:\n")
                for i in range(max(0, len(cv_time)-3), len(cv_time)):
                    info_f.write(f"    点{i+1}: 时间={cv_time[i]:.2f}分钟, 电压={cv_voltage[i]:.4f}V, 电流={cv_current[i]:.4f}A\n")
                
                info_f.write("\n" + "-" * 50 + "\n\n")
            
            # 如果需要分循环保存，将每个循环的CV数据保存为单独的JSON文件
            if separate_cycles:
                # 使用序号和充电策略命名文件，确保使用安全的充电策略
                cycle_file = os.path.join(cycles_dir, f'{int(cycle_number)}_policy_{safe_charge_policy}.json')
                with open(cycle_file, 'w') as f:
                    json.dump(cycle_cv_data, f, indent=2)
    
    # 更新有CV阶段的循环数
    cv_summary['cycles_with_cv'] = len(cv_phases_data)
    
    if not cv_phases_data:
        logger.warning(f"电池 {battery_id} 没有提取到CV阶段数据")
        return
    
    # 计算统计信息的平均值、最小值、最大值
    stats_to_add = {}
    for stat_name, stat_values in cv_summary['cv_stats'].items():
        if stat_values and not stat_name.endswith(('_avg', '_min', '_max')):
            stats_to_add[f'{stat_name}_avg'] = float(np.mean(stat_values))
            stats_to_add[f'{stat_name}_min'] = float(min(stat_values))
            stats_to_add[f'{stat_name}_max'] = float(max(stat_values))
    
    # 将计算的统计值添加到cv_summary中
    cv_summary['cv_stats'].update(stats_to_add)
    
    # 保存CV阶段统计信息为JSON文件，使用充电策略命名
    cv_summary_file = os.path.join(battery_info_dir, f'{file_prefix}_cv_summary.json')
    with open(cv_summary_file, 'w') as f:
        json.dump(cv_summary, f, indent=2)
    
    # 如果需要保存合并的CV数据文件
    if save_combined:
        # 保存CV阶段数据为JSON文件，使用电池ID和充电策略命名
        cv_phases_file = os.path.join(battery_output_dir, f'{file_prefix}_cv_phases.json')
        with open(cv_phases_file, 'w') as f:
            json.dump(cv_phases_data, f, indent=2)
    
    # 可视化CV阶段数据
    visualize_cv_data(battery_id, cv_phases_data, battery_info_dir, battery_charge_policy)
    
    end_time = time.time()
    logger.info(f"电池 {battery_id} 处理完成，提取了 {len(cv_phases_data)} 个循环的CV阶段数据，耗时: {end_time - start_time:.2f}秒")

def visualize_cv_data(battery_id: str, cv_phases_data: Dict[int, Dict], output_dir: str, charge_policy: str = 'unknown') -> None:
    """
    可视化CV阶段数据
    
    参数:
        battery_id (str): 电池ID
        cv_phases_data (Dict[int, Dict]): CV阶段数据
        output_dir (str): 输出目录
        charge_policy (str): 充电策略，用于文件命名
    """
    if not cv_phases_data:
        return
    
    # 确保电池ID和充电策略不包含无效字符
    safe_battery_id = ''.join(c for c in battery_id if c.isprintable() and c != '\0' and c != ' ')
    safe_charge_policy = ''.join(c for c in charge_policy if c.isprintable() and c != '\0' and c != ' ')
    
    # 创建图表目录
    plots_dir = os.path.join(output_dir, 'plots')
    os.makedirs(plots_dir, exist_ok=True)
    
    # 创建文件名前缀，包含电池ID和充电策略
    file_prefix = f"{safe_battery_id}_policy_{safe_charge_policy}"
    
    # 可视化第一个循环的CV阶段数据
    cycle_numbers = sorted(cv_phases_data.keys())
    first_cycle = cycle_numbers[0]
    cv_data = cv_phases_data[first_cycle]
    
    # 创建图形
    plt.figure(figsize=(12, 8))
    
    # 绘制电压-时间曲线
    plt.subplot(2, 1, 1)
    plt.plot(cv_data['time'], cv_data['voltage'], 'b-', linewidth=2)
    plt.ylabel('电压 (V)')
    plt.title(f'电池 {battery_id} 循环 {first_cycle} 恒压阶段 (策略: {charge_policy})')
    plt.grid(True)
    
    # 绘制电流-时间曲线
    plt.subplot(2, 1, 2)
    plt.plot(cv_data['time'], cv_data['current'], 'r-', linewidth=2)
    plt.xlabel('时间 (分钟)')
    plt.ylabel('电流 (A)')
    plt.grid(True)
    
    # 保存图形，使用充电策略命名
    plt.tight_layout()
    plt.savefig(os.path.join(plots_dir, f'{file_prefix}_cycle_{first_cycle}_cv.png'), dpi=300)
    plt.close()
    
    # 可视化所有循环的电流曲线
    if len(cycle_numbers) > 1:
        plt.figure(figsize=(15, 10))
        
        # 创建颜色映射
        colors = plt.cm.viridis(np.linspace(0, 1, min(len(cycle_numbers), 10)))
        
        # 选择要显示的循环（如果循环太多，只显示部分）
        cycles_to_show = cycle_numbers
        if len(cycles_to_show) > 10:
            # 选择均匀分布的10个循环
            indices = np.linspace(0, len(cycles_to_show) - 1, 10, dtype=int)
            cycles_to_show = [cycles_to_show[i] for i in indices]
        
        # 绘制电流-时间曲线
        for i, cycle_number in enumerate(cycles_to_show):
            cv_data = cv_phases_data[cycle_number]
            plt.plot(cv_data['time'], cv_data['current'], 
                     color=colors[i % len(colors)], linewidth=2, 
                     label=f'循环 {cycle_number}')
        
        plt.xlabel('时间 (分钟)')
        plt.ylabel('电流 (A)')
        plt.title(f'电池 {battery_id} 多个循环的恒压阶段电流曲线 (策略: {charge_policy})')
        plt.grid(True)
        plt.legend()
        
        # 保存图形，使用充电策略命名
        plt.tight_layout()
        plt.savefig(os.path.join(plots_dir, f'{file_prefix}_multiple_cycles_cv.png'), dpi=300)
        plt.close()
        
    # 创建电压-电流曲线（V-I曲线）
    plt.figure(figsize=(10, 8))
    
    # 选择要显示的循环
    cycles_to_show = cycle_numbers
    if len(cycles_to_show) > 5:
        # 选择均匀分布的5个循环
        indices = np.linspace(0, len(cycles_to_show) - 1, 5, dtype=int)
        cycles_to_show = [cycles_to_show[i] for i in indices]
    
    # 绘制电压-电流曲线
    for i, cycle_number in enumerate(cycles_to_show):
        cv_data = cv_phases_data[cycle_number]
        plt.plot(cv_data['voltage'], cv_data['current'], 
                 color=colors[i % len(colors)], linewidth=2, 
                 label=f'循环 {cycle_number}')
    
    plt.xlabel('电压 (V)')
    plt.ylabel('电流 (A)')
    plt.title(f'电池 {battery_id} 恒压阶段电压-电流曲线 (策略: {charge_policy})')
    plt.grid(True)
    plt.legend()
    
    # 保存图形，使用充电策略命名
    plt.tight_layout()
    plt.savefig(os.path.join(plots_dir, f'{file_prefix}_vi_curves.png'), dpi=300)
    plt.close()

def test_cv_extraction(data_dir: str, output_dir: str):
    """
    测试函数：提取单个电池数据并验证恒压阶段是否准确
    
    参数:
        data_dir (str): MIT_mat数据集路径
        output_dir (str): 输出目录
    """
    logger.info("===== 开始测试恒压阶段提取 =====")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建电池数据加载器
    voltage_threshold = 3.6  # MIT_mat数据集的恒压阈值
    
    loader = BatteryDataLoader(
        data_dir=data_dir,
        cache_dir=os.path.join(output_dir, 'cache'),
        voltage_threshold=voltage_threshold,
        tolerance=0.03,  # 容差设置，但实际会使用严格的±0.002V
        process_all_cells=False,  # 只处理第一个电池
        current_rate_threshold=0.05,
        strict_voltage_range=True
    )
    
    # 加载单个电池数据
    battery_files = loader.get_file_list()
    
    if not battery_files:
        logger.error(f"在{data_dir}中未找到MIT_mat电池数据文件")
        return
    
    # 只处理第一个文件
    file_path = battery_files[0]
    file_name = os.path.basename(file_path)
    logger.info(f"处理文件: {file_name}")
    
    # 加载MAT文件
    data = loader.load_mat_file(file_path)
    
    # 处理MIT数据集，返回电池数据
    battery_dict = loader._process_mit_mat_data(data, file_path)
    
    if not battery_dict:
        logger.error("未提取到任何电池数据")
        return
    
    # 获取第一个电池数据
    battery_id = list(battery_dict.keys())[0]
    battery_data = battery_dict[battery_id]
    
    # 分析恒压阶段数据
    cycles_with_cv = 0
    cycles_total = len(battery_data.get('cycles', {}))
    cv_voltage_stats = []
    cv_current_stats = []
    
    # 创建测试报告文件
    report_path = os.path.join(output_dir, f"{battery_id}_cv_test_report.txt")
    with open(report_path, 'w') as f:
        f.write(f"电池ID: {battery_id}\n")
        f.write(f"总循环数: {cycles_total}\n")
        f.write(f"恒压阈值: {voltage_threshold}V (±0.002V)\n")
        f.write("\n==== 恒压阶段详细分析 ====\n\n")
        
        # 遍历循环
        for cycle_num, cycle_data in battery_data.get('cycles', {}).items():
            cv_data = cycle_data.get('cv_phase')
            if cv_data:
                cycles_with_cv += 1
                cv_time = cv_data.get('time')
                cv_voltage = cv_data.get('voltage')
                cv_current = cv_data.get('current')
                
                # 计算电压和电流的统计信息
                v_mean = np.mean(cv_voltage)
                v_min = np.min(cv_voltage)
                v_max = np.max(cv_voltage)
                v_std = np.std(cv_voltage)
                i_start = cv_current[0] if len(cv_current) > 0 else None
                i_end = cv_current[-1] if len(cv_current) > 0 else None
                i_mean = np.mean(cv_current)
                
                # 检查电压是否都在恒压范围内
                v_in_range = np.sum((cv_voltage >= voltage_threshold - 0.002) & 
                                   (cv_voltage <= voltage_threshold + 0.002))
                v_percent_in_range = v_in_range / len(cv_voltage) * 100
                
                # 检查电流在1.0±0.002A范围内的点的数量
                cc_points = np.sum((cv_current >= 0.998) & (cv_current <= 1.002))
                
                # 保存统计信息
                cv_voltage_stats.append((v_mean, v_min, v_max, v_std))
                cv_current_stats.append((i_start, i_end, i_mean))
                
                # 写入报告
                f.write(f"循环 {cycle_num}:\n")
                f.write(f"  点数: {len(cv_time)}\n")
                f.write(f"  电压范围: {v_min:.6f}V - {v_max:.6f}V, 均值: {v_mean:.6f}V, 标准差: {v_std:.6f}V\n")
                f.write(f"  电压在3.6±0.002V范围内的点数: {v_in_range}/{len(cv_voltage)} ({v_percent_in_range:.2f}%)\n")
                f.write(f"  电流范围: {min(cv_current):.6f}A - {max(cv_current):.6f}A, 均值: {i_mean:.6f}A\n")
                f.write(f"  电流在1.0±0.002A范围内的点数: {cc_points}\n")
                f.write(f"  首末电流值: 开始={i_start:.6f}A, 结束={i_end:.6f}A\n")
                f.write(f"  持续时间: {cv_time[-1] - cv_time[0]:.2f}分钟\n")
                
                # 输出前5个点和后5个点的信息
                f.write("\n  前5个数据点:\n")
                for i in range(min(5, len(cv_time))):
                    f.write(f"    #{i}: 时间={cv_time[i]:.2f}分钟, 电压={cv_voltage[i]:.6f}V, 电流={cv_current[i]:.6f}A\n")
                
                f.write("\n  后5个数据点:\n")
                for i in range(max(0, len(cv_time) - 5), len(cv_time)):
                    f.write(f"    #{i}: 时间={cv_time[i]:.2f}分钟, 电压={cv_voltage[i]:.6f}V, 电流={cv_current[i]:.6f}A\n")
                
                f.write("\n" + "-" * 50 + "\n\n")
                
                # 生成可视化
                plt.figure(figsize=(12, 10))
                
                # 电压图
                plt.subplot(3, 1, 1)
                plt.plot(cv_time, cv_voltage, 'b-', linewidth=1.5)
                plt.axhline(y=voltage_threshold + 0.002, color='r', linestyle='--', linewidth=0.8)
                plt.axhline(y=voltage_threshold - 0.002, color='r', linestyle='--', linewidth=0.8)
                plt.ylabel('电压 (V)')
                plt.title(f'电池 {battery_id} 循环 {cycle_num} 恒压阶段分析')
                plt.grid(True)
                
                # 电流图
                plt.subplot(3, 1, 2)
                plt.plot(cv_time, cv_current, 'g-', linewidth=1.5)
                plt.axhline(y=1.002, color='r', linestyle='--', linewidth=0.8)
                plt.axhline(y=0.998, color='r', linestyle='--', linewidth=0.8)
                plt.ylabel('电流 (A)')
                plt.grid(True)
                
                # 电压与电流的关系图
                plt.subplot(3, 1, 3)
                plt.plot(cv_voltage, cv_current, 'o-', linewidth=1)
                plt.axvline(x=voltage_threshold + 0.002, color='r', linestyle='--', linewidth=0.8)
                plt.axvline(x=voltage_threshold - 0.002, color='r', linestyle='--', linewidth=0.8)
                plt.axhline(y=1.002, color='r', linestyle='--', linewidth=0.8)
                plt.axhline(y=0.998, color='r', linestyle='--', linewidth=0.8)
                plt.xlabel('电压 (V)')
                plt.ylabel('电流 (A)')
                plt.grid(True)
                
                plt.tight_layout()
                plt.savefig(os.path.join(output_dir, f"{battery_id}_cycle_{cycle_num}_cv_test.png"), dpi=150)
                plt.close()
                
                # 只处理第一个循环的恒压段
                break
        
        # 写入总结信息
        f.write("\n==== 测试总结 ====\n\n")
        f.write(f"总循环数: {cycles_total}\n")
        f.write(f"有恒压段的循环数: {cycles_with_cv}\n")
        f.write(f"恒压提取率: {cycles_with_cv/cycles_total*100:.2f}%\n")
    
    logger.info(f"测试报告已保存到: {report_path}")
    logger.info("===== 恒压阶段提取测试完成 =====")

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="处理MIT_mat数据集，提取CV阶段数据并保存为JSON")
    parser.add_argument("--data-dir", "-d", type=str, default=r"E:\SOH+LP_rawdata_MIT_HUST_XJTU_TJU\MIT_rawdata_mat",
                       help="MIT_mat数据目录（默认：E:/SOH+LP_rawdata_MIT_HUST_XJTU_TJU/MIT_rawdata_mat）")
    parser.add_argument("--output-dir", "-o", type=str, default="cv_data_output/MIT_mat",
                       help="输出目录")
    parser.add_argument("--info-dir", "-i", type=str, default=None,
                       help="CV信息输出目录，默认与output-dir相同")
    parser.add_argument("--max-batteries", "-m", type=int, default=None,
                       help="最多处理的电池数量")
    parser.add_argument("--separate-cycles", "-sc", action="store_true",
                       help="是否将每个循环的CV数据保存为单独的文件")
    parser.add_argument("--no-combined", "-nc", action="store_true",
                       help="不保存合并的CV数据文件")
    parser.add_argument("--group-by-policy", "-gbp", action="store_true",
                       help="是否按充电策略分组保存电池数据")
    parser.add_argument("--process-all-cells", "-pac", action="store_true", default=True,
                       help="处理.mat文件中的所有电池数据，默认为True")
    parser.add_argument("--current-rate-threshold", "-crt", type=float, default=0.05,
                       help="电流变化率阈值，用于区分恒流和恒压阶段，默认为0.05")
    parser.add_argument("--strict-voltage-range", "-svr", action="store_true", default=True,
                       help="是否使用严格的电压范围条件(±0.002V)，默认为True")
    parser.add_argument("--test", "-t", action="store_true",
                       help="运行测试模式，仅提取一个电池的数据并验证恒压阶段")
    parser.add_argument("--test-output-dir", "-tod", type=str, default="test_output/cv_test",
                       help="测试输出目录，默认为test_output/cv_test")
    
    args = parser.parse_args()
    
    # 检查数据目录是否存在
    if not os.path.exists(args.data_dir):
        logger.error(f"MIT_mat数据目录不存在: {args.data_dir}")
        return
    
    # 如果是测试模式，只运行测试函数
    if args.test:
        test_output_dir = args.test_output_dir
        os.makedirs(test_output_dir, exist_ok=True)
        logger.info(f"运行测试模式，输出将保存到: {test_output_dir}")
        test_cv_extraction(args.data_dir, test_output_dir)
        return
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 设置CV信息输出目录
    info_dir = args.info_dir if args.info_dir else args.output_dir
    os.makedirs(info_dir, exist_ok=True)
    logger.info(f"CV信息将保存到: {info_dir}")
    
    # 记录总处理时间
    total_start_time = time.time()
    
    # 处理MIT_mat数据集
    process_dataset(
        args.data_dir, 
        args.output_dir, 
        args.max_batteries,
        args.separate_cycles,
        not args.no_combined,
        args.info_dir,
        args.group_by_policy,
        args.process_all_cells,
        args.current_rate_threshold,
        args.strict_voltage_range
    )
    
    total_end_time = time.time()
    logger.info(f"MIT_mat数据集处理完成，总耗时: {total_end_time - total_start_time:.2f}秒")

if __name__ == "__main__":
    main() 