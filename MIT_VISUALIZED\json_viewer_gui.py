import sys
import os
import json
import pandas as pd
from PyQt5.QtWidgets import (QApplication, QMainWindow, QFileDialog, QTreeView, QTreeWidget, QTreeWidgetItem,
                             QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QTableView, QTabWidget,
                             QSplitter, QLabel, QMessageBox, QHeaderView)
from PyQt5.QtCore import Qt, QAbstractTableModel, QModelIndex
from PyQt5.QtGui import QFont, QColor

class PandasModel(QAbstractTableModel):
    """用于在QTableView中显示pandas DataFrame的模型"""
    
    def __init__(self, data):
        super().__init__()
        self._data = data

    def rowCount(self, parent=QModelIndex()):
        return self._data.shape[0]

    def columnCount(self, parent=QModelIndex()):
        return self._data.shape[1]

    def data(self, index, role=Qt.DisplayRole):
        if index.isValid():
            if role == Qt.DisplayRole:
                return str(self._data.iloc[index.row(), index.column()])
            if role == Qt.BackgroundRole and index.row() % 2 == 0:
                return QColor(Qt.lightGray)
        return None

    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if role == Qt.DisplayRole:
            if orientation == Qt.Horizontal:
                return str(self._data.columns[section])
            if orientation == Qt.Vertical:
                return str(self._data.index[section])
        return None


class JsonViewerApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.json_data = None
        self.current_key = None
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle('JSON文件可视化查看器')
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建顶部按钮布局
        button_layout = QHBoxLayout()
        
        # 打开文件按钮
        self.open_button = QPushButton('打开JSON文件')
        self.open_button.clicked.connect(self.open_file)
        button_layout.addWidget(self.open_button)
        
        # 文件路径标签
        self.file_label = QLabel('未选择文件')
        button_layout.addWidget(self.file_label, stretch=1)
        
        main_layout.addLayout(button_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 创建左侧树状视图
        self.tree_widget = QTreeWidget()
        self.tree_widget.setHeaderLabel('JSON结构')
        self.tree_widget.itemClicked.connect(self.on_tree_item_clicked)
        splitter.addWidget(self.tree_widget)
        
        # 创建右侧表格视图容器
        self.tab_widget = QTabWidget()
        self.normal_table = QTableView()
        self.normal_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.transposed_table = QTableView()
        self.transposed_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        self.tab_widget.addTab(self.normal_table, '普通视图')
        self.tab_widget.addTab(self.transposed_table, '转置视图')
        splitter.addWidget(self.tab_widget)
        
        # 设置分割器比例
        splitter.setSizes([300, 900])
        
        main_layout.addWidget(splitter)
        
    def open_file(self):
        file_path, _ = QFileDialog.getOpenFileName(self, '选择JSON文件', '', 'JSON文件 (*.json)')
        
        if file_path:
            try:
                with open(file_path, 'r') as f:
                    self.json_data = json.load(f)
                
                self.file_label.setText(file_path)
                self.populate_tree()
                QMessageBox.information(self, '成功', f'已成功加载JSON文件: {os.path.basename(file_path)}')
            except Exception as e:
                QMessageBox.critical(self, '错误', f'无法加载JSON文件: {str(e)}')
    
    def populate_tree(self):
        self.tree_widget.clear()
        
        if not self.json_data:
            return
        
        # 添加顶层项
        for key in self.json_data.keys():
            item = QTreeWidgetItem([key])
            self.tree_widget.addTopLevelItem(item)
            self.add_children(item, self.json_data[key])
        
        # 展开第一级
        self.tree_widget.expandToDepth(0)
    
    def add_children(self, parent_item, json_obj, max_depth=2, current_depth=0):
        """递归添加子项到树状视图"""
        if current_depth >= max_depth:
            # 达到最大深度，只显示类型信息
            if isinstance(json_obj, dict):
                child = QTreeWidgetItem([f"字典 ({len(json_obj)} 个键)"])
                parent_item.addChild(child)
            elif isinstance(json_obj, list):
                child = QTreeWidgetItem([f"列表 ({len(json_obj)} 个元素)"])
                parent_item.addChild(child)
            return
        
        if isinstance(json_obj, dict):
            for key, value in json_obj.items():
                child = QTreeWidgetItem([str(key)])
                parent_item.addChild(child)
                self.add_children(child, value, max_depth, current_depth + 1)
        elif isinstance(json_obj, list):
            if len(json_obj) > 0:
                # 只显示前几个元素
                max_items = min(5, len(json_obj))
                for i in range(max_items):
                    child = QTreeWidgetItem([f"[{i}]"])
                    parent_item.addChild(child)
                    self.add_children(child, json_obj[i], max_depth, current_depth + 1)
                
                if len(json_obj) > max_items:
                    parent_item.addChild(QTreeWidgetItem([f"... 还有 {len(json_obj) - max_items} 个元素"]))
        else:
            # 基本类型，直接显示值
            value_str = str(json_obj)
            if len(value_str) > 50:
                value_str = value_str[:50] + "..."
            child = QTreeWidgetItem([value_str])
            parent_item.addChild(child)
    
    def on_tree_item_clicked(self, item, column):
        """当树状视图中的项被点击时调用"""
        path = self.get_item_path(item)
        if not path:
            return
        
        # 获取选中项的数据
        data = self.get_data_from_path(path)
        if data is None:
            return
        
        # 显示数据
        self.display_data(data, path[-1] if path else "Root")
    
    def get_item_path(self, item):
        """获取树状项的完整路径"""
        path = []
        while item is not None:
            path.insert(0, item.text(0))
            item = item.parent()
        return path
    
    def get_data_from_path(self, path):
        """根据路径获取JSON数据"""
        if not self.json_data or not path:
            return None
        
        current = self.json_data
        # 跳过第一个元素，因为它是顶层键
        for key in path:
            if isinstance(current, dict) and key in current:
                current = current[key]
            elif isinstance(current, list) and key.startswith('[') and key.endswith(']'):
                try:
                    index = int(key[1:-1])
                    if 0 <= index < len(current):
                        current = current[index]
                    else:
                        return None
                except ValueError:
                    return None
            else:
                return None
        
        return current
    
    def display_data(self, data, title):
        """在表格视图中显示数据"""
        # 清除现有表格
        self.normal_table.setModel(None)
        self.transposed_table.setModel(None)
        
        if isinstance(data, dict):
            # 转换为DataFrame
            df = pd.DataFrame.from_dict(data, orient='index')
            
            # 设置普通视图
            normal_model = PandasModel(df)
            self.normal_table.setModel(normal_model)
            
            # 设置转置视图
            transposed_model = PandasModel(df.T)
            self.transposed_table.setModel(transposed_model)
            
            # 更新标签
            self.tab_widget.setTabText(0, f'普通视图 - {title}')
            self.tab_widget.setTabText(1, f'转置视图 - {title}')
        elif isinstance(data, list) and all(isinstance(item, dict) for item in data):
            # 列表中的所有项都是字典，可以转换为DataFrame
            df = pd.DataFrame(data)
            
            # 设置普通视图
            normal_model = PandasModel(df)
            self.normal_table.setModel(normal_model)
            
            # 设置转置视图
            transposed_model = PandasModel(df.T)
            self.transposed_table.setModel(transposed_model)
            
            # 更新标签
            self.tab_widget.setTabText(0, f'普通视图 - {title}')
            self.tab_widget.setTabText(1, f'转置视图 - {title}')
        else:
            # 不是可以直接转换为表格的数据类型
            QMessageBox.information(self, '信息', f'所选项 "{title}" 不能以表格形式显示。\n\n类型: {type(data).__name__}')


if __name__ == '__main__':
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion风格，在所有平台上看起来都不错
    
    window = JsonViewerApp()
    window.show()
    
    sys.exit(app.exec_())