# MIT电池数据恒压阶段提取工具

这是一个改进版的MIT电池数据处理工具，用于从MAT格式文件中提取恒压(CV)阶段数据和容量效率(CE)信息。

## 主要改进

1. **命令行参数支持** - 不再使用硬编码路径
2. **可配置输出路径** - 支持自定义输出目录
3. **并行处理** - 支持多进程并行处理，大幅提升处理速度
4. **进度显示** - 实时显示处理进度
5. **配置文件支持** - 支持JSON格式配置文件
6. **完善的日志系统** - 详细的日志记录和错误处理

## 安装依赖

```bash
pip install numpy h5py tqdm
```

## 使用方法

### 1. 基本用法

```bash
# 最简单的用法，指定输入目录
python mit_mat_by_h5py_to_json_CV_i-t-Q-CE.py --input-dir /path/to/mat/files

# 指定输入和输出目录
python mit_mat_by_h5py_to_json_CV_i-t-Q-CE.py --input-dir /path/to/mat/files --output-dir /path/to/output
```

### 2. 高级用法

```bash
# 使用4个进程并行处理
python mit_mat_by_h5py_to_json_CV_i-t-Q-CE.py --input-dir /path/to/mat/files --workers 4

# 自定义恒压阈值和容差
python mit_mat_by_h5py_to_json_CV_i-t-Q-CE.py --input-dir /path/to/mat/files --cv-voltage 3.6 --tolerance 0.001

# 启用调试日志
python mit_mat_by_h5py_to_json_CV_i-t-Q-CE.py --input-dir /path/to/mat/files --log-level DEBUG

# 禁用进度条
python mit_mat_by_h5py_to_json_CV_i-t-Q-CE.py --input-dir /path/to/mat/files --no-progress
```

### 3. 使用配置文件

```bash
# 使用配置文件
python mit_mat_by_h5py_to_json_CV_i-t-Q-CE.py --config config_example.json

# 配置文件和命令行参数混合使用（命令行参数优先级更高）
python mit_mat_by_h5py_to_json_CV_i-t-Q-CE.py --config config_example.json --workers 8
```

## 配置文件格式

创建一个JSON格式的配置文件（参考 `config_example.json`）：

```json
{
  "input_dir": "/path/to/mat/files",
  "output_dir": "/path/to/output",
  "workers": 4,
  "cv_voltage": 3.6,
  "tolerance": 0.001,
  "no_progress": false,
  "log_level": "INFO"
}
```

## 命令行参数说明

| 参数 | 简写 | 类型 | 默认值 | 说明 |
|------|------|------|--------|------|
| `--input-dir` | `-i` | str | 必需 | 输入目录路径（包含MAT文件） |
| `--output-dir` | `-o` | str | 与输入目录相同 | 输出目录路径 |
| `--config` | `-c` | str | 无 | 配置文件路径（JSON格式） |
| `--workers` | `-w` | int | CPU核心数 | 并行进程数 |
| `--cv-voltage` | | float | 3.6 | 恒压阶段电压阈值（V） |
| `--tolerance` | | float | 0.001 | 电压容差（V） |
| `--log-level` | | str | INFO | 日志级别（DEBUG/INFO/WARNING/ERROR） |
| `--no-progress` | | bool | False | 禁用进度条显示 |

## 输出文件

- **JSON文件**: 每个MAT文件对应一个JSON文件，包含恒压阶段数据和容量效率信息
- **日志文件**: `cv_extraction.log` - 详细的处理日志

## 输出数据结构

```json
{
  "b3c0": {
    "summary": {
      "QC": [充电容量数组],
      "QD": [放电容量数组],
      "CE": [容量效率数组]
    },
    "cv_cycles": {
      "0": {"I": [...], "Qc": [...], "V": [...], "t": [...]},
      "1": {"I": [...], "Qc": [...], "V": [...], "t": [...]}
    }
  }
}
```

## 性能优化

1. **并行处理**: 使用 `--workers` 参数设置合适的进程数（通常为CPU核心数）
2. **内存管理**: 大文件会自动使用流式处理
3. **进度监控**: 实时显示处理进度和预估完成时间

## 错误处理

- 所有错误都会记录到日志文件中
- 单个文件处理失败不会影响其他文件
- 处理完成后会显示成功/失败统计

## 示例

```bash
# 处理MIT数据集，使用8个进程，输出到指定目录
python mit_mat_by_h5py_to_json_CV_i-t-Q-CE.py \
  --input-dir "E:/MIT_rawdata_mat" \
  --output-dir "E:/MIT_processed" \
  --workers 8 \
  --cv-voltage 3.6 \
  --tolerance 0.001 \
  --log-level INFO
```

## 注意事项

1. 确保有足够的磁盘空间存储输出文件
2. 并行进程数不要超过CPU核心数太多
3. 大文件处理时注意内存使用情况
4. 定期检查日志文件了解处理状态
