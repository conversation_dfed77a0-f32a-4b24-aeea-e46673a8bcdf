import os
import numpy as np
import h5py
import pickle
import json
import traceback

# 检查路径是否存在
path = r"E:\SOH+LP_rawdata_MIT_HUST_XJTU_TJU\MIT_rawdata_mat"
if not os.path.exists(path):
    print(f"错误: 路径 '{path}' 不存在，请检查")
    exit(1)

print(f"开始处理文件夹: {path}")
print(f"文件夹中包含 {len([f for f in os.listdir(path) if '.mat' in f])} 个.mat文件")

# 自定义JSON编码器处理NumPy类型
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        return super(NumpyEncoder, self).default(obj)

def extract_cv_phase(V, I, Qc, t, cv_voltage=3.6, tolerance=0.001):
    """
    提取恒压阶段数据
    参数:
        V: 电压数组
        I: 电流数组  
        Qc: 充电容量数组
        t: 时间数组
        cv_voltage: 恒压阈值 (默认3.6V)
        tolerance: 电压容差 (默认±0.002V)
    """
    if len(V) == 0:
        return None
    
    # 找到电压在恒压范围内的点
    cv_mask = np.abs(V - cv_voltage) <= tolerance
    cv_indices = np.where(cv_mask)[0]
    
    if len(cv_indices) == 0:
        return None
    
    # 找到有效的恒压阶段起始和结束点
    start_idx = cv_indices[0]
    end_idx = cv_indices[-1]
    
    # 检查中间是否有超过连续3个点不在范围内
    consecutive_out = 0
    valid_start = start_idx
    
    for i in range(start_idx, end_idx + 1):
        if not cv_mask[i]:
            consecutive_out += 1
            if consecutive_out > 3:
                # 找到下一个在范围内的点作为新起始点
                next_valid = np.where(cv_mask[i:])[0]
                if len(next_valid) > 0:
                    valid_start = i + next_valid[0]
                    consecutive_out = 0
                else:
                    return None
        else:
            consecutive_out = 0
    
    # 提取恒压阶段数据
    cv_data = {
        'I': I[valid_start:end_idx+1],
        'Qc': Qc[valid_start:end_idx+1], 
        'V': V[valid_start:end_idx+1],
        't': t[valid_start:end_idx+1]
    }
    
    return cv_data

for f_name in os.listdir(path):
    if ".mat" in f_name:
        try:
            print(f"\n开始处理文件: {f_name}")
            base_name = f_name.split('_batchdata')[0]
            data_path = os.path.join(path, f_name)
            f = h5py.File(data_path)
            print(f"文件已打开，键列表: {list(f.keys())}")
            batch = f["batch"]
            print(f"批次键列表: {list(batch.keys())}")
            num_cells = batch["summary"].shape[0]
            print(f"单元格数量: {num_cells}")
            bat_dict = {}
            
            for i in range(num_cells):
                print(f"  处理单元格 {i+1}/{num_cells}")
                
                # 提取summary数据
                summary_QC = np.hstack(f[batch['summary'][i, 0]]['QCharge'][0, :].tolist())
                summary_QD = np.hstack(f[batch['summary'][i, 0]]['QDischarge'][0, :].tolist())
                
                # 计算CE (容量效率)
                CE = np.divide(summary_QD, summary_QC, out=np.zeros_like(summary_QD), where=summary_QC!=0)
                
                summary = {
                    'QC': summary_QC, 
                    'QD': summary_QD, 
                    'CE': CE
                }
                
                # 提取循环数据中的恒压阶段
                cycles = f[batch['cycles'][i, 0]]
                cv_cycles = {}
                
                for j in range(cycles['I'].shape[0]):
                    I = np.hstack((f[cycles['I'][j, 0]][:]))
                    Qc = np.hstack((f[cycles['Qc'][j, 0]][:]))
                    V = np.hstack((f[cycles['V'][j, 0]][:]))
                    t = np.hstack((f[cycles['t'][j, 0]][:]))
                    
                    # 提取恒压阶段数据
                    cv_data = extract_cv_phase(V, I, Qc, t)
                    if cv_data is not None:
                        cv_cycles[str(j)] = cv_data
                
                cell_dict = {
                    'summary': summary, 
                    'cv_cycles': cv_cycles
                }
                key = 'b3c' + str(i)
                bat_dict[key] = cell_dict
                print(f"  已添加单元格 {key}, 恒压循环数: {len(cv_cycles)}")
            
            save_name = f'{base_name}_CV_CE.json'
            full_path = os.path.join(path, save_name)
            print(f"保存文件到: {full_path}")
            with open(full_path, 'w') as fp:
                json.dump(bat_dict, fp, indent=2, cls=NumpyEncoder)
                print(f"文件 {save_name} 保存成功!")
            
            f.close()
            
        except Exception as e:
            print(f"处理文件 {f_name} 时出错:")
            print(traceback.format_exc())
            continue

print("所有文件处理完成!")
