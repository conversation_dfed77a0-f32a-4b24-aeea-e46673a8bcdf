import os
import numpy as np
import h5py
import pickle
import json
import traceback
import argparse
import multiprocessing as mp
from tqdm import tqdm
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Tuple


# 设置日志
def setup_logging(log_level: str = "INFO") -> None:
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("cv_extraction.log", encoding="utf-8"),
        ],
    )


# 自定义JSON编码器处理NumPy类型
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        return super(NumpyEncoder, self).default(obj)


def extract_cv_phase(V, I, Qc, t, cv_voltage=3.6, tolerance=0.001):
    """
    提取恒压阶段数据
    参数:
        V: 电压数组
        I: 电流数组
        Qc: 充电容量数组
        t: 时间数组
        cv_voltage: 恒压阈值 (默认3.6V)
        tolerance: 电压容差 (默认±0.002V)
    """
    if len(V) == 0:
        return None

    # 找到电压在恒压范围内的点
    cv_mask = np.abs(V - cv_voltage) <= tolerance
    cv_indices = np.where(cv_mask)[0]

    if len(cv_indices) == 0:
        return None

    # 找到有效的恒压阶段起始和结束点
    start_idx = cv_indices[0]
    end_idx = cv_indices[-1]

    # 检查中间是否有超过连续3个点不在范围内
    consecutive_out = 0
    valid_start = start_idx

    for i in range(start_idx, end_idx + 1):
        if not cv_mask[i]:
            consecutive_out += 1
            if consecutive_out > 3:
                # 找到下一个在范围内的点作为新起始点
                next_valid = np.where(cv_mask[i:])[0]
                if len(next_valid) > 0:
                    valid_start = i + next_valid[0]
                    consecutive_out = 0
                else:
                    return None
        else:
            consecutive_out = 0

    # 提取恒压阶段数据
    cv_data = {
        "I": I[valid_start : end_idx + 1],
        "Qc": Qc[valid_start : end_idx + 1],
        "V": V[valid_start : end_idx + 1],
        "t": t[valid_start : end_idx + 1],
    }

    return cv_data


def process_single_file(args: Tuple[str, str, str, float, float]) -> Optional[str]:
    """
    处理单个MAT文件

    参数:
        args: (file_path, input_dir, output_dir, cv_voltage, tolerance)

    返回:
        str: 成功处理的文件名，失败返回None
    """
    file_path, input_dir, output_dir, cv_voltage, tolerance = args
    f_name = os.path.basename(file_path)

    try:
        logging.info(f"开始处理文件: {f_name}")
        base_name = f_name.split("_batchdata")[0]

        with h5py.File(file_path, "r") as f:
            logging.debug(f"文件已打开，键列表: {list(f.keys())}")
            batch = f["batch"]
            logging.debug(f"批次键列表: {list(batch.keys())}")
            num_cells = batch["summary"].shape[0]
            logging.info(f"文件 {f_name} 包含 {num_cells} 个单元格")

            bat_dict = {}

            for i in range(num_cells):
                logging.debug(f"  处理单元格 {i+1}/{num_cells}")

                # 提取summary数据
                summary_QC = np.hstack(
                    f[batch["summary"][i, 0]]["QCharge"][0, :].tolist()
                )
                summary_QD = np.hstack(
                    f[batch["summary"][i, 0]]["QDischarge"][0, :].tolist()
                )

                # 计算CE (容量效率)
                CE = np.divide(
                    summary_QD,
                    summary_QC,
                    out=np.zeros_like(summary_QD),
                    where=summary_QC != 0,
                )

                summary = {"QC": summary_QC, "QD": summary_QD, "CE": CE}

                # 提取循环数据中的恒压阶段
                cycles = f[batch["cycles"][i, 0]]
                cv_cycles = {}

                for j in range(cycles["I"].shape[0]):
                    I = np.hstack((f[cycles["I"][j, 0]][:]))
                    Qc = np.hstack((f[cycles["Qc"][j, 0]][:]))
                    V = np.hstack((f[cycles["V"][j, 0]][:]))
                    t = np.hstack((f[cycles["t"][j, 0]][:]))

                    # 提取恒压阶段数据
                    cv_data = extract_cv_phase(V, I, Qc, t, cv_voltage, tolerance)
                    if cv_data is not None:
                        cv_cycles[str(j)] = cv_data

                cell_dict = {"summary": summary, "cv_cycles": cv_cycles}
                key = "b3c" + str(i)
                bat_dict[key] = cell_dict
                logging.debug(f"  已添加单元格 {key}, 恒压循环数: {len(cv_cycles)}")

            # 保存文件
            save_name = f"{base_name}_CV_CE.json"
            full_path = os.path.join(output_dir, save_name)

            with open(full_path, "w", encoding="utf-8") as fp:
                json.dump(bat_dict, fp, indent=2, cls=NumpyEncoder, ensure_ascii=False)

            logging.info(f"文件 {save_name} 保存成功到: {full_path}")
            return f_name

    except Exception as e:
        logging.error(f"处理文件 {f_name} 时出错: {str(e)}")
        logging.debug(traceback.format_exc())
        return None


def load_config(config_path: str) -> Dict[str, Any]:
    """
    加载配置文件

    参数:
        config_path: 配置文件路径

    返回:
        Dict: 配置参数字典
    """
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            config = json.load(f)
        logging.info(f"已加载配置文件: {config_path}")
        return config
    except Exception as e:
        logging.error(f"加载配置文件失败: {str(e)}")
        return {}


def parse_arguments() -> argparse.Namespace:
    """
    解析命令行参数

    返回:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(
        description="MIT电池数据恒压阶段提取工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python %(prog)s --input-dir /path/to/mat/files
  python %(prog)s --input-dir /path/to/mat/files --output-dir /path/to/output
  python %(prog)s --config config.json
  python %(prog)s --input-dir /path/to/mat/files --workers 4 --cv-voltage 3.6
        """,
    )

    parser.add_argument(
        "--input-dir", "-i", type=str, help="输入目录路径（包含MAT文件）"
    )

    parser.add_argument(
        "--output-dir", "-o", type=str, help="输出目录路径（默认与输入目录相同）"
    )

    parser.add_argument("--config", "-c", type=str, help="配置文件路径（JSON格式）")

    parser.add_argument(
        "--workers",
        "-w",
        type=int,
        default=mp.cpu_count(),
        help=f"并行进程数（默认: {mp.cpu_count()}）",
    )

    parser.add_argument(
        "--cv-voltage", type=float, default=3.6, help="恒压阶段电压阈值（默认: 3.6V）"
    )

    parser.add_argument(
        "--tolerance", type=float, default=0.001, help="电压容差（默认: 0.001V）"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="日志级别（默认: INFO）",
    )

    parser.add_argument("--no-progress", action="store_true", help="禁用进度条显示")

    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 设置日志
    setup_logging(args.log_level)

    # 加载配置文件（如果提供）
    config = {}
    if args.config:
        config = load_config(args.config)

    # 合并配置：命令行参数优先级高于配置文件
    input_dir = args.input_dir or config.get("input_dir")
    output_dir = args.output_dir or config.get("output_dir") or input_dir
    workers = (
        args.workers
        if args.workers != mp.cpu_count()
        else config.get("workers", mp.cpu_count())
    )
    cv_voltage = (
        args.cv_voltage if args.cv_voltage != 3.6 else config.get("cv_voltage", 3.6)
    )
    tolerance = (
        args.tolerance if args.tolerance != 0.001 else config.get("tolerance", 0.001)
    )
    no_progress = args.no_progress or config.get("no_progress", False)

    # 验证必需参数
    if not input_dir:
        logging.error("必须提供输入目录路径（--input-dir 或配置文件中的 input_dir）")
        return 1

    if not os.path.exists(input_dir):
        logging.error(f"输入目录不存在: {input_dir}")
        return 1

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 获取所有MAT文件
    mat_files = []
    for root, _, files in os.walk(input_dir):
        for file in files:
            if file.endswith(".mat"):
                mat_files.append(os.path.join(root, file))

    if not mat_files:
        logging.warning(f"在目录 {input_dir} 中未找到MAT文件")
        return 0

    logging.info(f"找到 {len(mat_files)} 个MAT文件")
    logging.info(f"输入目录: {input_dir}")
    logging.info(f"输出目录: {output_dir}")
    logging.info(f"并行进程数: {workers}")
    logging.info(f"恒压阈值: {cv_voltage}V")
    logging.info(f"电压容差: {tolerance}V")

    # 准备处理参数
    process_args = [
        (file_path, input_dir, output_dir, cv_voltage, tolerance)
        for file_path in mat_files
    ]

    # 开始处理
    start_time = time.time()
    successful_files = []

    if workers == 1:
        # 单进程处理
        iterator = process_args
        if not no_progress:
            try:
                iterator = tqdm(process_args, desc="处理文件", unit="文件")
            except ImportError:
                logging.warning("tqdm未安装，无法显示进度条")

        for args_tuple in iterator:
            result = process_single_file(args_tuple)
            if result:
                successful_files.append(result)
    else:
        # 多进程处理
        with mp.Pool(workers) as pool:
            if not no_progress:
                try:
                    # 使用tqdm显示进度
                    results = list(
                        tqdm(
                            pool.imap(process_single_file, process_args),
                            total=len(process_args),
                            desc="处理文件",
                            unit="文件",
                        )
                    )
                except ImportError:
                    logging.warning("tqdm未安装，无法显示进度条")
                    results = pool.map(process_single_file, process_args)
            else:
                results = pool.map(process_single_file, process_args)

            successful_files = [r for r in results if r is not None]

    # 处理完成统计
    end_time = time.time()
    processing_time = end_time - start_time

    logging.info(f"处理完成!")
    logging.info(f"总文件数: {len(mat_files)}")
    logging.info(f"成功处理: {len(successful_files)}")
    logging.info(f"失败文件: {len(mat_files) - len(successful_files)}")
    logging.info(f"总耗时: {processing_time:.2f}秒")

    if len(successful_files) < len(mat_files):
        logging.warning("部分文件处理失败，请检查日志文件 cv_extraction.log")

    return 0


if __name__ == "__main__":
    import time

    exit(main())
